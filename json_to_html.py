#!/usr/bin/env python3
"""
JSON to HTML Converter

This script converts JSON files to standalone HTML files for easy viewing.
Usage: python json_to_html.py input.json [output.html]
"""

import json
import sys
import os
import argparse
from pathlib import Path

def escape_html(text):
    """Escape HTML special characters in text"""
    if not isinstance(text, str):
        return str(text)
    return (text.replace('&', '&amp;')
                .replace('<', '&lt;')
                .replace('>', '&gt;')
                .replace('"', '&quot;')
                .replace("'", '&#039;'))

def format_value_for_display(value, level=0):
    """Format a value for HTML display"""
    if value is None:
        return '<span class="json-value null">null</span>'
    
    if value == "undefined":
        return '<span class="json-value null">undefined</span>'
    
    value_type = type(value).__name__
    
    if value_type == 'str':
        # For strings, use tooltip approach for consistency
        if len(value) > 100:
            truncated = escape_html(value[:100]) + '...'
            return f'<span class="json-value string" title="{escape_html(value)}">{truncated}</span>'
        elif len(value) > 50:
            return f'<span class="json-value string" title="{escape_html(value)}">{escape_html(value)}</span>'
        else:
            return f'<span class="json-value string">{escape_html(value)}</span>'
    
    elif value_type == 'int' or value_type == 'float':
        return f'<span class="json-value number">{value}</span>'
    
    elif value_type == 'bool':
        return f'<span class="json-value boolean">{str(value).lower()}</span>'
    
    elif value_type == 'list':
        if len(value) == 0:
            return '<span class="badge bg-secondary">Empty Array</span>'
        elif len(value) <= 5 and all(not isinstance(item, (dict, list)) or item is None for item in value):
            # Simple array with primitives
            items = [format_value_for_display(item) for item in value]
            return f'<span class="badge bg-info">Array[{len(value)}]</span> [{", ".join(items)}]'
        else:
            # Complex array - render inline
            return render_nested_object(value, level)
    
    elif value_type == 'dict':
        if len(value) == 0:
            return '<span class="badge bg-secondary">Empty Object</span>'
        else:
            return render_nested_object(value, level)
    
    else:
        return escape_html(str(value))

def render_nested_object(obj, level=0):
    """Render a nested object or array as HTML"""
    if level > 10:  # Prevent infinite recursion
        return '<div class="alert alert-warning">Maximum nesting level reached</div>'
    
    # Limit the indentation level for better display
    display_level = min(level, 5)
    
    if isinstance(obj, list):
        if len(obj) == 0:
            return '<span class="badge bg-secondary">Empty Array</span>'
        
        # Check if array contains only primitives
        is_primitive_array = all(not isinstance(item, (dict, list)) or item is None for item in obj)
        
        if is_primitive_array:
            items = [format_value_for_display(item) for item in obj]
            return f'<span class="badge bg-info">Array[{len(obj)}]</span> [{", ".join(items)}]'
        
        # Array of objects
        html = f'<div class="nested-level-{display_level}">'
        for index, item in enumerate(obj):
            html += f'<div class="mb-2"><div class="card"><div class="card-body p-2"><strong>Item {index + 1}</strong>'
            if isinstance(item, (dict, list)) and item is not None:
                html += render_nested_object(item, level + 1)
            else:
                html += f'<span class="json-value">{format_value_for_display(item)}</span>'
            html += '</div></div></div>'
        html += '</div>'
        return html
    
    # Regular object (dict)
    html = f'<div class="nested-object nested-level-{display_level}">'
    for key, value in obj.items():
        html += '<div class="row mb-2">'
        html += f'<div class="col-sm-3"><strong class="json-key">{escape_html(str(key))}</strong></div>'
        html += '<div class="col-sm-9">'
        
        if isinstance(value, (dict, list)) and value is not None:
            if len(value) == 0:
                if isinstance(value, dict):
                    html += '<span class="badge bg-secondary">Empty Object</span>'
                else:
                    html += '<span class="badge bg-secondary">Empty Array</span>'
            else:
                # For nested objects, render inline with proper indentation
                html += render_nested_object(value, level + 1)
        else:
            html += format_value_for_display(value)
        html += '</div></div>'
    html += '</div>'
    
    return html

def render_json_to_html(data, title="JSON Viewer"):
    """Convert JSON data to a complete HTML document"""
    # Generate the HTML content
    content = ''
    
    if isinstance(data, list):
        # Handle arrays of objects
        if len(data) > 0 and isinstance(data[0], dict):
            content = '<div class="nested-structure">'
            for index, item in enumerate(data):
                content += f'''
                    <div class="card mb-3 nested-item">
                        <div class="card-header">
                            <strong>Array Item {index + 1}</strong>
                        </div>
                        <div class="card-body">
                '''
                if isinstance(item, dict):
                    content += render_nested_object(item, 1)
                else:
                    content += f'<div class="row"><div class="col-12"><span class="json-value">{format_value_for_display(item)}</span></div></div>'
                content += '</div></div>'
            content += '</div>'
        else:
            # Simple array
            content = '<div class="table-container"><table class="table table-striped"><thead><tr><th>Index</th><th>Value</th></tr></thead><tbody>'
            for index, value in enumerate(data):
                content += f'<tr><td>{index}</td><td>{format_value_for_display(value)}</td></tr>'
            content += '</tbody></table></div>'
    elif isinstance(data, dict):
        # Handle objects
        content = '<div class="nested-structure">'
        content += render_nested_object(data, 0)
        content += '</div>'
    else:
        # Handle primitives
        content = f'<div class="alert alert-info">Value: {format_value_for_display(data)}</div>'
    
    # Create complete HTML document
    html_template = f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {{
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --table-header-bg: #e9ecef;
            --table-border: #dee2e6;
            --expand-btn-bg: #f1f1f1;
            --expand-btn-hover: #e2e6ea;
            --nesting-indent: 20px;
        }}

        [data-theme="dark"] {{
            --bs-body-bg: #121212;
            --bs-body-color: #e0e0e0;
            --bs-card-bg: #1e1e1e;
            --bs-card-border-color: #333;
            --table-header-bg: #2d2d2d;
            --table-border: #444;
            --expand-btn-bg: #333;
            --expand-btn-hover: #444;
            --bs-border-color: #444;
        }}

        body {{
            background-color: var(--bs-body-bg);
            color: var(--bs-body-color);
            transition: background-color 0.3s, color 0.3s;
            padding: 20px;
        }}

        .card {{
            background-color: var(--bs-card-bg);
            border-color: var(--bs-card-border-color);
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: box-shadow 0.3s;
        }}

        .card:hover {{
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }}

        .card-header {{
            background-color: rgba(0, 0, 0, 0.03);
            border-bottom: 1px solid var(--bs-card-border-color);
            font-weight: 500;
        }}

        [data-theme="dark"] .card-header {{
            background-color: #2a2a2a;
        }}

        .table-container {{
            overflow-x: auto;
            border-radius: 0.375rem;
            border: 1px solid var(--table-border);
        }}

        .table {{
            margin-bottom: 0;
            background-color: var(--bs-card-bg);
        }}

        .table th {{
            background-color: var(--table-header-bg);
            position: sticky;
            top: 0;
            border-bottom: 2px solid var(--table-border);
        }}

        .table td, .table th {{
            border-color: var(--table-border);
            vertical-align: middle;
        }}

        .nested-object {{
            display: block;
            margin-top: 0.5rem;
            padding: 0.5rem;
            background-color: rgba(0, 0, 0, 0.02);
            border-radius: 0.25rem;
            border: 1px solid var(--table-border);
        }}

        [data-theme="dark"] .nested-object {{
            background-color: #2a2a2a;
        }}

        .json-key {{
            font-weight: 600;
            color: var(--primary-color);
        }}

        [data-theme="dark"] .json-key {{
            color: #6ea8fe;
        }}

        .json-value {{
            word-break: break-word;
        }}

        .json-value.string {{
            color: var(--success-color);
        }}

        .json-value.number {{
            color: var(--info-color);
        }}

        .json-value.boolean {{
            color: var(--warning-color);
        }}

        .json-value.null {{
            color: var(--secondary-color);
            font-style: italic;
        }}

        .text-truncate-cell {{
            max-width: 200px;
        }}

        .badge-sm {{
            font-size: 0.7em;
        }}

        /* Nested structure styling */
        .nested-structure {{
            padding: 1rem;
        }}

        .nested-item {{
            margin-bottom: 1rem;
        }}

        .nested-item .card-header {{
            background-color: rgba(0, 0, 0, 0.03);
        }}

        [data-theme="dark"] .nested-item .card-header {{
            background-color: #2a2a2a;
        }}

        .nested-object .row {{
            margin-bottom: 0.5rem;
        }}

        .nested-object .row:last-child {{
            margin-bottom: 0;
        }}

        /* Improved nesting with better hierarchy */
        .nested-level-0 {{ margin-left: 0; }}
        .nested-level-1 {{ margin-left: calc(var(--nesting-indent) * 1); }}
        .nested-level-2 {{ margin-left: calc(var(--nesting-indent) * 2); }}
        .nested-level-3 {{ margin-left: calc(var(--nesting-indent) * 3); }}
        .nested-level-4 {{ margin-left: calc(var(--nesting-indent) * 4); }}
        .nested-level-5 {{ margin-left: calc(var(--nesting-indent) * 5); }}

        .nested-object .nested-object {{
            border-left: 2px solid var(--table-border);
            padding-left: 1rem;
            margin: 0.5rem 0;
        }}

        [data-theme="dark"] .nested-object .nested-object {{
            border-left-color: #444;
        }}

        /* Long text handling - tooltip only approach */
        .long-text-container {{
            cursor: pointer;
            border: 1px dashed transparent;
            padding: 5px;
            border-radius: 4px;
        }}

        .long-text-container:hover {{
            border-color: var(--primary-color);
            background-color: rgba(0, 123, 255, 0.05);
        }}

        /* Tooltip for full text */
        [title] {{
            position: relative;
        }}

        [title]:hover::after {{
            content: attr(title);
            position: absolute;
            bottom: 100%;
            left: 0;
            background-color: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            max-width: 300px;
            z-index: 1000;
            font-size: 0.9em;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }}

        /* Responsive adjustments */
        @media (max-width: 768px) {{
            .text-truncate-cell {{
                max-width: 100px;
            }}
            
            .table-sm td, .table-sm th {{
                padding: 0.25rem 0.5rem;
            }}
            
            .nested-structure {{
                padding: 0.5rem;
            }}
            
            .nested-level-1 {{ margin-left: 10px; }}
            .nested-level-2 {{ margin-left: 20px; }}
            .nested-level-3 {{ margin-left: 30px; }}
            .nested-level-4 {{ margin-left: 40px; }}
            .nested-level-5 {{ margin-left: 50px; }}
        }}

        /* Card styling for nested items */
        .nested-object .card {{
            margin-bottom: 0.5rem;
            background-color: rgba(0, 0, 0, 0.02);
        }}

        [data-theme="dark"] .nested-object .card {{
            background-color: #2a2a2a;
        }}

        .nested-object .card:last-child {{
            margin-bottom: 0;
        }}
        
        .header {{
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 10px;
            background-color: rgba(0, 123, 255, 0.1);
        }}
        
        [data-theme="dark"] .header {{
            background-color: rgba(100, 150, 255, 0.1);
        }}
        
        .content-container {{
            max-width: 1200px;
            margin: 0 auto;
        }}
    </style>
</head>
<body>
    <div class="content-container">
        <div class="header">
            <h1>{title}</h1>
            <p class="text-muted">Generated on {get_current_time()}</p>
        </div>
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">JSON Data</h5>
            </div>
            <div class="card-body">
                <div id="jsonOutput">
                    {content}
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Add dark mode toggle functionality
        document.addEventListener('DOMContentLoaded', function() {{
            // Check for dark mode preference in localStorage
            if (localStorage.getItem('darkMode') === 'enabled') {{
                document.body.setAttribute('data-theme', 'dark');
            }}
            
            // Add dark mode toggle to the page
            const header = document.querySelector('.header');
            const toggleContainer = document.createElement('div');
            toggleContainer.className = 'form-check form-switch';
            toggleContainer.innerHTML = `
                <input class="form-check-input" type="checkbox" id="darkModeToggle">
                <label class="form-check-label" for="darkModeToggle">Dark Mode</label>
            `;
            header.appendChild(toggleContainer);
            
            const darkModeToggle = document.getElementById('darkModeToggle');
            if (localStorage.getItem('darkMode') === 'enabled') {{
                darkModeToggle.checked = true;
            }}
            
            darkModeToggle.addEventListener('change', function() {{
                if (this.checked) {{
                    document.body.setAttribute('data-theme', 'dark');
                    localStorage.setItem('darkMode', 'enabled');
                }} else {{
                    document.body.removeAttribute('data-theme');
                    localStorage.setItem('darkMode', 'disabled');
                }}
            }});
        }});
    </script>
</body>
</html>'''
    
    return html_template

def get_current_time():
    """Get current time as string"""
    from datetime import datetime
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def main():
    parser = argparse.ArgumentParser(description='Convert JSON file to HTML view')
    parser.add_argument('input', help='Input JSON file path')
    parser.add_argument('output', nargs='?', help='Output HTML file path (optional)')
    parser.add_argument('-t', '--title', default='JSON Viewer', help='HTML page title')
    
    args = parser.parse_args()
    
    # Check if input file exists
    if not os.path.exists(args.input):
        print(f"Error: Input file '{args.input}' not found.")
        sys.exit(1)
    
    try:
        # Read and parse JSON file
        with open(args.input, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Generate HTML content
        html_content = render_json_to_html(data, args.title)
        
        # Determine output file name
        if args.output:
            output_file = args.output
        else:
            input_path = Path(args.input)
            output_file = input_path.with_suffix('.html')
        
        # Write HTML file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"Successfully converted '{args.input}' to '{output_file}'")
        
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in file '{args.input}': {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
