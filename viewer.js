// viewer.js - Final fix for Transpose View functionality

document.addEventListener('DOMContentLoaded', function() {
    // --- Element Selectors ---
    const jsonFileInput = document.getElementById('jsonFile');
    const loadFileBtn = document.getElementById('loadFileBtn');
    const jsonOutput = document.getElementById('jsonOutput');
    const darkModeToggle = document.getElementById('darkModeToggle');
    const searchInput = document.getElementById('searchInput');
    const clearSearchBtn = document.getElementById('clearSearchBtn');
    const viewToggle = document.getElementById('viewToggle');

    // --- State Variables ---
    let currentViewMode = 'normal';
    let currentJsonData = null;

    // --- Event Listeners ---

    // View mode change (Normal/Transpose)
    viewToggle.addEventListener('change', function() {
        currentViewMode = this.value;
        if (currentJsonData) {
            renderJSON(currentJsonData);
        }
    });

    // Dark mode toggle
    darkModeToggle.addEventListener('change', function() {
        if (this.checked) {
            document.body.setAttribute('data-theme', 'dark');
            localStorage.setItem('darkMode', 'enabled');
        } else {
            document.body.removeAttribute('data-theme');
            localStorage.setItem('darkMode', 'disabled');
        }
        updateModalTheme();
    });

    // Load file button
    loadFileBtn.addEventListener('click', function() {
        if (jsonFileInput.files.length === 0) {
            alert('Please select a JSON file first.');
            return;
        }
        loadFile(jsonFileInput.files[0]);
    });

    // Auto-load on file selection
    jsonFileInput.addEventListener('change', function() {
        if (this.files.length > 0) {
            loadFile(this.files[0]);
        }
    });
    
    // --- Core Rendering Functions ---

    /**
     * Main function to render JSON data based on the selected view mode.
     */
    function renderJSON(data) {
        currentJsonData = data; // Store the data for re-rendering
        jsonOutput.innerHTML = ''; // Clear previous output

        if (currentViewMode === 'transpose') {
            // Render as a flat, Excel-style table
            renderTransposeView(data);
        } else {
            // Render as a hierarchical, nested view
            renderNormalView(data);
        }
        // Always re-attach event listeners for dynamic content
        addFieldViewEventListeners();
    }

    // Expose renderJSON globally for testing
    window.renderJSON = renderJSON;

    /**
     * Renders the data in the default hierarchical view.
     */
    function renderNormalView(data) {
        if (Array.isArray(data)) {
            if (data.length > 0 && typeof data[0] === 'object' && data[0] !== null) {
                renderArrayOfObjects(data);
            } else {
                renderSimpleArray(data);
            }
        } else if (typeof data === 'object' && data !== null) {
            renderObjectAsNestedStructure(data);
        } else {
            jsonOutput.innerHTML = `<div class="alert alert-info">Value: ${formatValueForDisplay(data)}</div>`;
        }
    }

    /**
     * Renders the data in a Word document-style transpose view.
     * Organizes JSON data like a human would in a structured document.
     */
    function renderTransposeView(data) {
        let html = '<div class="word-document-style">';
        html += renderWordStyleContent(data, '', 0);
        html += '</div>';
        jsonOutput.innerHTML = html;
    }

    /**
     * Main function to render content in Word document style.
     * Recursively processes JSON structure to create sections, tables, and subsections.
     */
    function renderWordStyleContent(data, sectionTitle, level) {
        let html = '';

        if (Array.isArray(data)) {
            // Arrays become tables if they contain objects, otherwise simple lists
            if (data.length > 0 && typeof data[0] === 'object' && data[0] !== null && !Array.isArray(data[0])) {
                html += renderArrayAsWordTable(data, sectionTitle, level);
            } else {
                html += renderSimpleArrayAsWordList(data, sectionTitle, level);
            }
        } else if (typeof data === 'object' && data !== null) {
            html += renderObjectAsWordSections(data, sectionTitle, level);
        } else {
            // Simple values
            if (sectionTitle) {
                html += `<div class="word-section level-${level}">`;
                html += `<h${Math.min(level + 2, 6)} class="word-heading">${escapeHtml(sectionTitle)}</h${Math.min(level + 2, 6)}>`;
                html += `<div class="word-content">${formatValueForDisplay(data)}</div>`;
                html += '</div>';
            } else {
                html += `<div class="word-content">${formatValueForDisplay(data)}</div>`;
            }
        }

        return html;
    }

    /**
     * Renders an array of objects as a Word-style table.
     * Creates proper table headers from object keys and handles nested objects intelligently.
     */
    function renderArrayAsWordTable(arrayData, sectionTitle, level) {
        if (arrayData.length === 0) {
            return `<div class="word-section level-${level}">
                        <h${Math.min(level + 2, 6)} class="word-heading">${escapeHtml(sectionTitle || 'Empty Array')}</h${Math.min(level + 2, 6)}>
                        <div class="alert alert-info">No data available</div>
                    </div>`;
        }

        let html = '';
        if (sectionTitle) {
            html += `<div class="word-section level-${level}">`;
            html += `<h${Math.min(level + 2, 6)} class="word-heading">${escapeHtml(sectionTitle)}</h${Math.min(level + 2, 6)}>`;
        }

        // Analyze all objects to get complete set of columns
        const columnInfo = analyzeArrayForColumns(arrayData);

        html += '<div class="word-table-container">';
        html += '<table class="table table-striped word-table">';

        // Create table headers
        html += '<thead><tr>';
        columnInfo.columns.forEach(col => {
            html += `<th class="word-table-header">${escapeHtml(col.displayName)}</th>`;
        });
        html += '</tr></thead>';

        // Create table rows
        html += '<tbody>';
        arrayData.forEach((item, index) => {
            if (typeof item === 'object' && item !== null) {
                html += '<tr>';
                columnInfo.columns.forEach(col => {
                    const value = getNestedValue(item, col.path);
                    html += `<td class="word-table-cell">${formatValueForWordTable(value, col.isComplex)}</td>`;
                });
                html += '</tr>';
            }
        });
        html += '</tbody></table></div>';

        if (sectionTitle) {
            html += '</div>';
        }

        return html;
    }

    /**
     * Analyzes an array of objects to determine optimal column structure.
     * Decides whether to flatten nested objects or create subsections.
     */
    function analyzeArrayForColumns(arrayData) {
        const allKeys = new Map(); // key -> {count, isComplex, maxComplexity}

        arrayData.forEach(item => {
            if (typeof item === 'object' && item !== null) {
                analyzeObjectKeys(item, '', allKeys);
            }
        });

        const columns = [];
        allKeys.forEach((info, key) => {
            // Decide whether to flatten or keep as complex based on size and complexity
            const shouldFlatten = info.maxComplexity <= 3 && info.avgSize <= 5;

            if (shouldFlatten && info.isComplex) {
                // Flatten this nested object
                const flatKeys = getFlattenedKeys(arrayData, key);
                flatKeys.forEach(flatKey => {
                    columns.push({
                        path: flatKey.path,
                        displayName: flatKey.displayName,
                        isComplex: false
                    });
                });
            } else {
                columns.push({
                    path: key,
                    displayName: key.split('.').pop() || key,
                    isComplex: info.isComplex
                });
            }
        });

        return { columns };
    }

    /**
     * Recursively analyzes object keys to understand structure complexity.
     */
    function analyzeObjectKeys(obj, prefix, allKeys) {
        Object.keys(obj).forEach(key => {
            const fullKey = prefix ? `${prefix}.${key}` : key;
            const value = obj[key];

            if (!allKeys.has(fullKey)) {
                allKeys.set(fullKey, { count: 0, isComplex: false, maxComplexity: 0, avgSize: 0 });
            }

            const info = allKeys.get(fullKey);
            info.count++;

            if (typeof value === 'object' && value !== null) {
                info.isComplex = true;
                if (Array.isArray(value)) {
                    info.maxComplexity = Math.max(info.maxComplexity, 2);
                    info.avgSize = Math.max(info.avgSize, value.length);
                } else {
                    const objSize = Object.keys(value).length;
                    info.maxComplexity = Math.max(info.maxComplexity, 3);
                    info.avgSize = Math.max(info.avgSize, objSize);

                    // Recursively analyze nested objects
                    if (objSize <= 5) { // Only analyze small nested objects for flattening
                        analyzeObjectKeys(value, fullKey, allKeys);
                    }
                }
            }
        });
    }
    
    /**
     * Gets flattened keys for a nested object that should be expanded into columns.
     */
    function getFlattenedKeys(arrayData, baseKey) {
        const flatKeys = [];
        const sampleObj = arrayData.find(item => getNestedValue(item, baseKey) !== undefined);

        if (sampleObj) {
            const nestedObj = getNestedValue(sampleObj, baseKey);
            if (typeof nestedObj === 'object' && nestedObj !== null && !Array.isArray(nestedObj)) {
                Object.keys(nestedObj).forEach(key => {
                    flatKeys.push({
                        path: `${baseKey}.${key}`,
                        displayName: `${baseKey.split('.').pop()} ${key}`.replace(/([A-Z])/g, ' $1').trim()
                    });
                });
            }
        }

        return flatKeys;
    }

    /**
     * Gets a nested value from an object using dot notation path.
     */
    function getNestedValue(obj, path) {
        if (!path) return obj;

        const keys = path.split('.');
        let current = obj;

        for (const key of keys) {
            if (current === null || current === undefined || typeof current !== 'object') {
                return undefined;
            }
            current = current[key];
        }

        return current;
    }

    /**
     * Formats values specifically for Word-style table cells.
     */
    function formatValueForWordTable(value, isComplex = false) {
        if (value === null || value === undefined) {
            return '<span class="json-value null">—</span>';
        }

        if (typeof value === 'object') {
            if (Array.isArray(value)) {
                if (value.length === 0) return '<span class="json-value array">[]</span>';
                if (value.length <= 3) {
                    return `<span class="json-value array">[${value.map(v =>
                        typeof v === 'object' ? JSON.stringify(v) : String(v)
                    ).join(', ')}]</span>`;
                }
                return `<span class="json-value array">[${value.length} items]</span>`;
            } else {
                const keys = Object.keys(value);
                if (keys.length === 0) return '<span class="json-value object">{}</span>';
                if (keys.length <= 2) {
                    return `<div class="word-object-inline">${keys.map(k =>
                        `<span class="word-key-value"><strong>${escapeHtml(k)}:</strong> ${formatValueForWordTable(value[k])}</span>`
                    ).join(' ')}</div>`;
                }
                return `<span class="json-value object">{${keys.length} properties}</span>`;
            }
        }

        return formatValueForDisplay(value, true);
    }

    /**
     * Renders objects as Word-style sections with proper hierarchy.
     */
    function renderObjectAsWordSections(obj, sectionTitle, level) {
        let html = '';

        if (sectionTitle) {
            html += `<div class="word-section level-${level}">`;
            html += `<h${Math.min(level + 2, 6)} class="word-heading">${escapeHtml(sectionTitle)}</h${Math.min(level + 2, 6)}>`;
        }

        Object.entries(obj).forEach(([key, value]) => {
            if (Array.isArray(value)) {
                // Arrays become tables or lists
                html += renderWordStyleContent(value, key, level + 1);
            } else if (typeof value === 'object' && value !== null) {
                // Nested objects become subsections
                html += renderWordStyleContent(value, key, level + 1);
            } else {
                // Simple key-value pairs
                html += `<div class="word-property">`;
                html += `<span class="word-property-key">${escapeHtml(key)}:</span> `;
                html += `<span class="word-property-value">${formatValueForDisplay(value)}</span>`;
                html += `</div>`;
            }
        });

        if (sectionTitle) {
            html += '</div>';
        }

        return html;
    }

    /**
     * Renders simple arrays as Word-style lists.
     */
    function renderSimpleArrayAsWordList(arrayData, sectionTitle, level) {
        let html = '';

        if (sectionTitle) {
            html += `<div class="word-section level-${level}">`;
            html += `<h${Math.min(level + 2, 6)} class="word-heading">${escapeHtml(sectionTitle)}</h${Math.min(level + 2, 6)}>`;
        }

        if (arrayData.length === 0) {
            html += '<div class="alert alert-info">Empty list</div>';
        } else {
            html += '<ul class="word-list">';
            arrayData.forEach(item => {
                html += `<li class="word-list-item">${formatValueForDisplay(item)}</li>`;
            });
            html += '</ul>';
        }

        if (sectionTitle) {
            html += '</div>';
        }

        return html;
    }

    // --- Hierarchical (Normal) View Functions ---
    
    function renderArrayOfObjects(arrayData) {
        let html = '<div class="nested-structure">';
        arrayData.forEach((item, index) => {
            html += `
                <div class="card mb-2 nested-item">
                    <div class="card-header py-2"><strong>Array Item ${index + 1}</strong></div>
                    <div class="card-body py-1">${renderNestedObject(item, 1)}</div>
                </div>`;
        });
        html += '</div>';
        jsonOutput.innerHTML = html;
    }

    function renderSimpleArray(arrayData) {
        let html = '<div class="table-container"><table class="table table-striped table-sm"><thead><tr><th>Index</th><th>Value</th></tr></thead><tbody>';
        arrayData.forEach((value, index) => {
            html += `<tr><td>${index}</td><td>${formatValueForDisplay(value)}</td></tr>`;
        });
        html += '</tbody></table></div>';
        jsonOutput.innerHTML = html;
    }

    function renderObjectAsNestedStructure(obj) {
        jsonOutput.innerHTML = `<div class="nested-structure">${renderNestedObject(obj, 0)}</div>`;
    }

    function renderNestedObject(obj, level) {
        if (level > 10) return `<div class="alert alert-warning">Max nesting reached</div>`;

        const displayLevel = Math.min(level, 5);
        let html = '';

        if (Array.isArray(obj)) {
            if (obj.length === 0) return '<span class="badge bg-secondary">Empty Array</span>';
            html += `<div class="nested-level-${displayLevel}">`;
            obj.forEach((item, index) => {
                html += `<div class="mb-1"><div class="card"><div class="card-body p-1"><strong>Item ${index + 1}</strong>`;
                html += renderNestedObject(item, level + 1);
                html += '</div></div></div>';
            });
            html += '</div>';
            return html;
        }
        
        if (typeof obj !== 'object' || obj === null) {
            return formatValueForDisplay(obj);
        }

        html += `<div class="nested-object nested-level-${displayLevel}">`;
        Object.entries(obj).forEach(([key, value]) => {
            html += `<div class="row mb-1">
                        <div class="col-sm-4 col-md-3"><strong class="json-key">${escapeHtml(key)}</strong></div>
                        <div class="col-sm-8 col-md-9">${renderNestedObject(value, level + 1)}</div>
                     </div>`;
        });
        html += '</div>';
        
        return html;
    }

    // --- Value Formatting & Display ---

    function formatValueForDisplay(value, isTableCell = false) {
        if (value === null) return '<span class="json-value null">null</span>';

        switch (typeof value) {
            case 'string':
                if (value.length > 80) {
                    const truncated = escapeHtml(value.substring(0, 80)) + '...';
                    // Don't show the '[view]' indicator in table cells to keep them clean
                    return isTableCell 
                        ? `<span class="json-value string" title="${escapeHtml(value)}">${truncated}</span>`
                        : `${truncated}<span class="long-text-indicator" data-full-text="${escapeHtml(value)}">[view]</span>`;
                }
                return `<span class="json-value string">${escapeHtml(value)}</span>`;
            case 'number':
                return `<span class="json-value number">${value}</span>`;
            case 'boolean':
                return `<span class="json-value boolean">${value}</span>`;
            case 'object':
                 return isTableCell ? formatValueForTableCell(value) : '[Complex Object]';
            default:
                return escapeHtml(String(value));
        }
    }
    
    // --- File Loading ---
    
    function loadFile(file) {
        const reader = new FileReader();
        reader.onload = e => {
            try {
                const jsonData = JSON.parse(e.target.result);
                renderJSON(jsonData);
            } catch (error) {
                jsonOutput.innerHTML = `<div class="alert alert-danger" role="alert"><h4>Error parsing JSON</h4><p>${error.message}</p></div>`;
            }
        };
        reader.onerror = () => {
            jsonOutput.innerHTML = `<div class="alert alert-danger" role="alert"><h4>Error reading file</h4></div>`;
        };
        reader.readAsText(file);
    }
    
    // --- Modal Handling ---

    function createFieldViewModal() {
        const overlay = document.createElement('div');
        overlay.id = 'fieldViewOverlay';
        overlay.className = 'field-view-overlay';
        
        const modal = document.createElement('div');
        modal.id = 'fieldViewModal';
        modal.className = 'field-view-modal';
        modal.innerHTML = `
            <div class="field-view-header"><h5 class="field-view-title">Field Content</h5></div>
            <div class="field-view-content" id="fieldViewContent"></div>
            <div class="field-view-footer"><button class="btn-close-field" id="closeFieldViewBottom">Close</button></div>`;
        
        document.body.appendChild(overlay);
        document.body.appendChild(modal);
        
        document.getElementById('closeFieldViewBottom').addEventListener('click', closeFieldView);
        overlay.addEventListener('click', closeFieldView);
    }

    function showFieldView(content) {
        document.getElementById('fieldViewContent').textContent = content;
        document.getElementById('fieldViewModal').style.display = 'flex';
        document.getElementById('fieldViewOverlay').style.display = 'block';
    }

    function closeFieldView() {
        document.getElementById('fieldViewModal').style.display = 'none';
        document.getElementById('fieldViewOverlay').style.display = 'none';
    }

    function updateModalTheme() {
        // This function would update the modal's theme if it were separate from the body's theme
    }

    function addFieldViewEventListeners() {
        jsonOutput.querySelectorAll('.long-text-indicator').forEach(indicator => {
            indicator.addEventListener('click', function(e) {
                e.stopPropagation();
                showFieldView(this.getAttribute('data-full-text'));
            });
        });
    }

    // --- Utility Functions ---
    
    function escapeHtml(unsafe) {
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    // --- Initial Setup ---
    clearSearchBtn.style.display = 'none';
    createFieldViewModal();
});
