// viewer.js - Final fix for Transpose View functionality

document.addEventListener('DOMContentLoaded', function() {
    // --- Element Selectors ---
    const jsonFileInput = document.getElementById('jsonFile');
    const loadFileBtn = document.getElementById('loadFileBtn');
    const jsonOutput = document.getElementById('jsonOutput');
    const darkModeToggle = document.getElementById('darkModeToggle');
    const searchInput = document.getElementById('searchInput');
    const clearSearchBtn = document.getElementById('clearSearchBtn');
    const viewToggle = document.getElementById('viewToggle');
    const downloadHtmlBtn = document.getElementById('downloadHtmlBtn');

    // --- State Variables ---
    let currentViewMode = 'normal';
    let currentJsonData = null;
    let currentFileName = null;

    // --- Event Listeners ---

    // View mode change (Normal/Transpose)
    viewToggle.addEventListener('change', function() {
        currentViewMode = this.value;
        if (currentJsonData) {
            renderJSON(currentJsonData);
        }
    });

    // Dark mode toggle
    darkModeToggle.addEventListener('change', function() {
        if (this.checked) {
            document.body.setAttribute('data-theme', 'dark');
            localStorage.setItem('darkMode', 'enabled');
        } else {
            document.body.removeAttribute('data-theme');
            localStorage.setItem('darkMode', 'disabled');
        }
        updateModalTheme();
    });

    // Load file button
    loadFileBtn.addEventListener('click', function() {
        if (jsonFileInput.files.length === 0) {
            alert('Please select a JSON file first.');
            return;
        }
        loadFile(jsonFileInput.files[0]);
    });

    // Auto-load on file selection
    jsonFileInput.addEventListener('change', function() {
        if (this.files.length > 0) {
            loadFile(this.files[0]);
        }
    });

    // Download HTML button
    downloadHtmlBtn.addEventListener('click', function() {
        if (currentJsonData) {
            downloadAsHtml();
        } else {
            alert('Please load a JSON file first.');
        }
    });
    
    // --- Core Rendering Functions ---

    /**
     * Main function to render JSON data based on the selected view mode.
     */
    function renderJSON(data) {
        currentJsonData = data; // Store the data for re-rendering
        jsonOutput.innerHTML = ''; // Clear previous output

        if (currentViewMode === 'transpose') {
            // Render as a flat, Excel-style table
            renderTransposeView(data);
        } else {
            // Render as a hierarchical, nested view
            renderNormalView(data);
        }
        // Always re-attach event listeners for dynamic content
        addFieldViewEventListeners();

        // Show download button when JSON is loaded
        downloadHtmlBtn.style.display = 'inline-block';
    }

    // Expose renderJSON globally for testing
    window.renderJSON = renderJSON;

    /**
     * Renders the data in the default hierarchical view.
     */
    function renderNormalView(data) {
        if (Array.isArray(data)) {
            if (data.length > 0 && typeof data[0] === 'object' && data[0] !== null) {
                renderArrayOfObjects(data);
            } else {
                renderSimpleArray(data);
            }
        } else if (typeof data === 'object' && data !== null) {
            renderObjectAsNestedStructure(data);
        } else {
            jsonOutput.innerHTML = `<div class="alert alert-info">Value: ${formatValueForDisplay(data)}</div>`;
        }
    }

    /**
     * Renders the data in a Word document-style transpose view.
     * Organizes JSON data like a human would in a structured document.
     */
    function renderTransposeView(data) {
        console.log('renderTransposeView called with data:', data);
        let html = '<div class="word-document-style">';
        html += renderWordStyleContent(data, '', 0);
        html += '</div>';
        console.log('Setting innerHTML with HTML:', html);
        jsonOutput.innerHTML = html;
    }

    /**
     * Main function to render content in Word document style.
     * Recursively processes JSON structure to create sections, tables, and subsections.
     */
    function renderWordStyleContent(data, sectionTitle, level) {
        let html = '';

        if (Array.isArray(data)) {
            // Arrays become tables if they contain objects, otherwise simple lists
            if (data.length > 0 && typeof data[0] === 'object' && data[0] !== null && !Array.isArray(data[0])) {
                html += renderArrayAsWordTable(data, sectionTitle, level);
            } else {
                html += renderSimpleArrayAsWordList(data, sectionTitle, level);
            }
        } else if (typeof data === 'object' && data !== null) {
            html += renderObjectAsWordSections(data, sectionTitle, level);
        } else {
            // Simple values
            if (sectionTitle) {
                html += `<div class="word-section level-${level}">`;
                html += `<h${Math.min(level + 2, 6)} class="word-heading">${escapeHtml(sectionTitle)}</h${Math.min(level + 2, 6)}>`;
                html += `<div class="word-content">${formatValueForDisplay(data)}</div>`;
                html += '</div>';
            } else {
                html += `<div class="word-content">${formatValueForDisplay(data)}</div>`;
            }
        }

        return html;
    }

    /**
     * Renders an array of objects as a Word-style table.
     * Creates proper table headers from object keys and handles nested objects intelligently.
     */
    function renderArrayAsWordTable(arrayData, sectionTitle, level) {
        console.log('renderArrayAsWordTable called with:', arrayData.length, 'items, title:', sectionTitle);
        if (arrayData.length === 0) {
            return `<div class="word-section level-${level}">
                        <h${Math.min(level + 2, 6)} class="word-heading">${escapeHtml(sectionTitle || 'Empty Array')}</h${Math.min(level + 2, 6)}>
                        <div class="alert alert-info">No data available</div>
                    </div>`;
        }

        let html = '';
        if (sectionTitle) {
            html += `<div class="word-section level-${level}">`;
            html += `<h${Math.min(level + 2, 6)} class="word-heading">${escapeHtml(sectionTitle)}</h${Math.min(level + 2, 6)}>`;
        }

        // Analyze all objects to get complete set of columns
        const columnInfo = analyzeArrayForColumns(arrayData);

        html += '<div class="word-table-container">';
        html += '<table class="table table-striped table-bordered word-table" style="border: 10px solid red !important;">';
        console.log('Creating table with classes: table table-striped table-bordered word-table');

        // Create table headers
        html += '<thead><tr>';
        columnInfo.columns.forEach(col => {
            html += `<th class="word-table-header">${escapeHtml(col.displayName)}</th>`;
        });
        html += '</tr></thead>';

        // Create table rows
        html += '<tbody>';
        arrayData.forEach((item, index) => {
            if (typeof item === 'object' && item !== null) {
                html += '<tr>';
                columnInfo.columns.forEach(col => {
                    const value = getNestedValue(item, col.path);
                    html += `<td class="word-table-cell">${formatValueForWordTable(value, col.isComplex)}</td>`;
                });
                html += '</tr>';
            }
        });
        html += '</tbody></table></div>';

        if (sectionTitle) {
            html += '</div>';
        }

        return html;
    }

    /**
     * Analyzes an array of objects to determine optimal column structure.
     * Decides whether to flatten nested objects or create subsections.
     */
    function analyzeArrayForColumns(arrayData) {
        const allKeys = new Map(); // key -> {count, isComplex, maxComplexity}

        arrayData.forEach(item => {
            if (typeof item === 'object' && item !== null) {
                analyzeObjectKeys(item, '', allKeys);
            }
        });

        const columns = [];
        allKeys.forEach((info, key) => {
            // Decide whether to flatten or keep as complex based on size and complexity
            const shouldFlatten = info.maxComplexity <= 3 && info.avgSize <= 5;

            if (shouldFlatten && info.isComplex) {
                // Flatten this nested object
                const flatKeys = getFlattenedKeys(arrayData, key);
                flatKeys.forEach(flatKey => {
                    columns.push({
                        path: flatKey.path,
                        displayName: flatKey.displayName,
                        isComplex: false
                    });
                });
            } else {
                columns.push({
                    path: key,
                    displayName: key.split('.').pop() || key,
                    isComplex: info.isComplex
                });
            }
        });

        return { columns };
    }

    /**
     * Recursively analyzes object keys to understand structure complexity.
     */
    function analyzeObjectKeys(obj, prefix, allKeys) {
        Object.keys(obj).forEach(key => {
            const fullKey = prefix ? `${prefix}.${key}` : key;
            const value = obj[key];

            if (!allKeys.has(fullKey)) {
                allKeys.set(fullKey, { count: 0, isComplex: false, maxComplexity: 0, avgSize: 0 });
            }

            const info = allKeys.get(fullKey);
            info.count++;

            if (typeof value === 'object' && value !== null) {
                info.isComplex = true;
                if (Array.isArray(value)) {
                    info.maxComplexity = Math.max(info.maxComplexity, 2);
                    info.avgSize = Math.max(info.avgSize, value.length);
                } else {
                    const objSize = Object.keys(value).length;
                    info.maxComplexity = Math.max(info.maxComplexity, 3);
                    info.avgSize = Math.max(info.avgSize, objSize);

                    // Recursively analyze nested objects
                    if (objSize <= 5) { // Only analyze small nested objects for flattening
                        analyzeObjectKeys(value, fullKey, allKeys);
                    }
                }
            }
        });
    }
    
    /**
     * Gets flattened keys for a nested object that should be expanded into columns.
     */
    function getFlattenedKeys(arrayData, baseKey) {
        const flatKeys = [];
        const sampleObj = arrayData.find(item => getNestedValue(item, baseKey) !== undefined);

        if (sampleObj) {
            const nestedObj = getNestedValue(sampleObj, baseKey);
            if (typeof nestedObj === 'object' && nestedObj !== null && !Array.isArray(nestedObj)) {
                Object.keys(nestedObj).forEach(key => {
                    flatKeys.push({
                        path: `${baseKey}.${key}`,
                        displayName: `${baseKey.split('.').pop()} ${key}`.replace(/([A-Z])/g, ' $1').trim()
                    });
                });
            }
        }

        return flatKeys;
    }

    /**
     * Gets a nested value from an object using dot notation path.
     */
    function getNestedValue(obj, path) {
        if (!path) return obj;

        const keys = path.split('.');
        let current = obj;

        for (const key of keys) {
            if (current === null || current === undefined || typeof current !== 'object') {
                return undefined;
            }
            current = current[key];
        }

        return current;
    }

    /**
     * Formats values specifically for Word-style table cells.
     */
    function formatValueForWordTable(value, isComplex = false) {
        if (value === null || value === undefined) {
            return '<span class="json-value null">—</span>';
        }

        if (typeof value === 'object') {
            if (Array.isArray(value)) {
                if (value.length === 0) return '<span class="json-value array">[]</span>';
                if (value.length <= 3) {
                    return `<span class="json-value array">[${value.map(v =>
                        typeof v === 'object' ? JSON.stringify(v) : String(v)
                    ).join(', ')}]</span>`;
                }
                return `<span class="json-value array">[${value.length} items]</span>`;
            } else {
                const keys = Object.keys(value);
                if (keys.length === 0) return '<span class="json-value object">{}</span>';
                if (keys.length <= 2) {
                    return `<div class="word-object-inline">${keys.map(k =>
                        `<span class="word-key-value"><strong>${escapeHtml(k)}:</strong> ${formatValueForWordTable(value[k])}</span>`
                    ).join(' ')}</div>`;
                }
                return `<span class="json-value object">{${keys.length} properties}</span>`;
            }
        }

        return formatValueForDisplay(value, true);
    }

    /**
     * Renders objects as Word-style sections with proper hierarchy.
     */
    function renderObjectAsWordSections(obj, sectionTitle, level) {
        let html = '';

        if (sectionTitle) {
            html += `<div class="word-section level-${level}">`;
            html += `<h${Math.min(level + 2, 6)} class="word-heading">${escapeHtml(sectionTitle)}</h${Math.min(level + 2, 6)}>`;
        }

        Object.entries(obj).forEach(([key, value]) => {
            if (Array.isArray(value)) {
                // Arrays become tables or lists
                html += renderWordStyleContent(value, key, level + 1);
            } else if (typeof value === 'object' && value !== null) {
                // Nested objects become subsections
                html += renderWordStyleContent(value, key, level + 1);
            } else {
                // Simple key-value pairs
                html += `<div class="word-property">`;
                html += `<span class="word-property-key">${escapeHtml(key)}:</span> `;
                html += `<span class="word-property-value">${formatValueForDisplay(value)}</span>`;
                html += `</div>`;
            }
        });

        if (sectionTitle) {
            html += '</div>';
        }

        return html;
    }

    /**
     * Renders simple arrays as Word-style lists.
     */
    function renderSimpleArrayAsWordList(arrayData, sectionTitle, level) {
        let html = '';

        if (sectionTitle) {
            html += `<div class="word-section level-${level}">`;
            html += `<h${Math.min(level + 2, 6)} class="word-heading">${escapeHtml(sectionTitle)}</h${Math.min(level + 2, 6)}>`;
        }

        if (arrayData.length === 0) {
            html += '<div class="alert alert-info">Empty list</div>';
        } else {
            html += '<ul class="word-list">';
            arrayData.forEach(item => {
                html += `<li class="word-list-item">${formatValueForDisplay(item)}</li>`;
            });
            html += '</ul>';
        }

        if (sectionTitle) {
            html += '</div>';
        }

        return html;
    }

    // --- Hierarchical (Normal) View Functions ---
    
    function renderArrayOfObjects(arrayData) {
        let html = '<div class="nested-structure">';
        arrayData.forEach((item, index) => {
            html += `
                <div class="card mb-2 nested-item">
                    <div class="card-header py-2"><strong>Array Item ${index + 1}</strong></div>
                    <div class="card-body py-1">${renderNestedObject(item, 1)}</div>
                </div>`;
        });
        html += '</div>';
        jsonOutput.innerHTML = html;
    }

    function renderSimpleArray(arrayData) {
        let html = '<div class="table-container"><table class="table table-striped table-bordered table-sm"><thead><tr><th>Index</th><th>Value</th></tr></thead><tbody>';
        arrayData.forEach((value, index) => {
            html += `<tr><td>${index}</td><td>${formatValueForDisplay(value)}</td></tr>`;
        });
        html += '</tbody></table></div>';
        jsonOutput.innerHTML = html;
    }

    function renderObjectAsNestedStructure(obj) {
        jsonOutput.innerHTML = `<div class="nested-structure">${renderNestedObject(obj, 0)}</div>`;
    }

    function renderNestedObject(obj, level) {
        if (level > 10) return `<div class="alert alert-warning">Max nesting reached</div>`;

        const displayLevel = Math.min(level, 5);
        let html = '';

        if (Array.isArray(obj)) {
            if (obj.length === 0) return '<span class="badge bg-secondary">Empty Array</span>';
            html += `<div class="nested-level-${displayLevel}">`;
            obj.forEach((item, index) => {
                html += `<div class="mb-1"><div class="card"><div class="card-body p-1"><strong>Item ${index + 1}</strong>`;
                html += renderNestedObject(item, level + 1);
                html += '</div></div></div>';
            });
            html += '</div>';
            return html;
        }
        
        if (typeof obj !== 'object' || obj === null) {
            return formatValueForDisplay(obj);
        }

        html += `<div class="nested-object nested-level-${displayLevel}">`;
        Object.entries(obj).forEach(([key, value]) => {
            html += `<div class="row mb-1">
                        <div class="col-sm-4 col-md-3"><strong class="json-key">${escapeHtml(key)}</strong></div>
                        <div class="col-sm-8 col-md-9">${renderNestedObject(value, level + 1)}</div>
                     </div>`;
        });
        html += '</div>';
        
        return html;
    }

    // --- Value Formatting & Display ---

    function formatValueForDisplay(value, isTableCell = false) {
        if (value === null) return '<span class="json-value null">null</span>';

        switch (typeof value) {
            case 'string':
                if (value.length > 80) {
                    const truncated = escapeHtml(value.substring(0, 80)) + '...';
                    // Don't show the '[view]' indicator in table cells to keep them clean
                    return isTableCell 
                        ? `<span class="json-value string" title="${escapeHtml(value)}">${truncated}</span>`
                        : `${truncated}<span class="long-text-indicator" data-full-text="${escapeHtml(value)}">[view]</span>`;
                }
                return `<span class="json-value string">${escapeHtml(value)}</span>`;
            case 'number':
                return `<span class="json-value number">${value}</span>`;
            case 'boolean':
                return `<span class="json-value boolean">${value}</span>`;
            case 'object':
                 return isTableCell ? formatValueForTableCell(value) : '[Complex Object]';
            default:
                return escapeHtml(String(value));
        }
    }
    
    // --- File Loading ---
    
    function loadFile(file) {
        currentFileName = file.name; // Store the filename for download
        const reader = new FileReader();
        reader.onload = e => {
            try {
                const jsonData = JSON.parse(e.target.result);
                renderJSON(jsonData);
            } catch (error) {
                jsonOutput.innerHTML = `<div class="alert alert-danger" role="alert"><h4>Error parsing JSON</h4><p>${error.message}</p></div>`;
            }
        };
        reader.onerror = () => {
            jsonOutput.innerHTML = `<div class="alert alert-danger" role="alert"><h4>Error reading file</h4></div>`;
        };
        reader.readAsText(file);
    }
    
    // --- Modal Handling ---

    function createFieldViewModal() {
        const overlay = document.createElement('div');
        overlay.id = 'fieldViewOverlay';
        overlay.className = 'field-view-overlay';
        
        const modal = document.createElement('div');
        modal.id = 'fieldViewModal';
        modal.className = 'field-view-modal';
        modal.innerHTML = `
            <div class="field-view-header"><h5 class="field-view-title">Field Content</h5></div>
            <div class="field-view-content" id="fieldViewContent"></div>
            <div class="field-view-footer"><button class="btn-close-field" id="closeFieldViewBottom">Close</button></div>`;
        
        document.body.appendChild(overlay);
        document.body.appendChild(modal);
        
        document.getElementById('closeFieldViewBottom').addEventListener('click', closeFieldView);
        overlay.addEventListener('click', closeFieldView);
    }

    function showFieldView(content) {
        document.getElementById('fieldViewContent').textContent = content;
        document.getElementById('fieldViewModal').style.display = 'flex';
        document.getElementById('fieldViewOverlay').style.display = 'block';
    }

    function closeFieldView() {
        document.getElementById('fieldViewModal').style.display = 'none';
        document.getElementById('fieldViewOverlay').style.display = 'none';
    }

    function updateModalTheme() {
        // This function would update the modal's theme if it were separate from the body's theme
    }

    function addFieldViewEventListeners() {
        jsonOutput.querySelectorAll('.long-text-indicator').forEach(indicator => {
            indicator.addEventListener('click', function(e) {
                e.stopPropagation();
                showFieldView(this.getAttribute('data-full-text'));
            });
        });
    }

    // --- Utility Functions ---
    
    function escapeHtml(unsafe) {
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    // --- Download Functionality ---

    function getCssContent() {
        // Return the CSS content as a string - this avoids CORS issues with fetch
        return `/* style.css - Updated with global view toggle */

:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --table-header-bg: #e9ecef;
    --table-border: #adb5bd;
    --nesting-indent: 12px; /* Very compact indentation */
    --modal-bg: #ffffff;
    --modal-text: #212529;
}

[data-theme="dark"] {
    --bs-body-bg: #121212;
    --bs-body-color: #e0e0e0;
    --bs-card-bg: #1e1e1e;
    --bs-card-border-color: #333;
    --table-header-bg: #2d2d2d;
    --table-border: #444;
    --bs-border-color: #444;
    --modal-bg: #1e1e1e;
    --modal-text: #e0e0e0;
}

body {
    background-color: var(--bs-body-bg);
    color: var(--bs-body-color);
    transition: background-color 0.3s, color 0.3s;
    padding: 5px;
    font-size: 0.85rem;
}

.card {
    background-color: var(--bs-card-bg);
    border-color: var(--bs-card-border-color);
    box-shadow: 0 0.05rem 0.1rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.2s;
    margin-bottom: 5px;
}

.card:hover {
    box-shadow: 0 0.2rem 0.4rem rgba(0, 0, 0, 0.1);
}

.json-container {
    font-family: 'Courier New', monospace;
    font-size: 0.75rem;
    line-height: 1.2;
    padding: 2px;
    margin: 1px 0;
}

.json-key {
    color: var(--primary-color);
    font-weight: bold;
    display: inline-block;
    margin-right: 0.25rem;
}

.json-value {
    display: inline-block;
    word-break: break-word;
}

.json-value.string {
    color: var(--success-color);
}

.json-value.number {
    color: var(--info-color);
}

.json-value.boolean {
    color: var(--warning-color);
}

.json-value.null {
    color: var(--secondary-color);
    font-style: italic;
}

.json-nested {
    margin-left: var(--nesting-indent);
    border-left: 1px solid var(--table-border);
    padding-left: 4px;
}

.json-array-item {
    margin: 1px 0;
    padding: 1px 0;
}

.json-object-property {
    margin: 1px 0;
    padding: 1px 0;
}

.long-text-indicator {
    background-color: var(--info-color);
    color: white;
    padding: 0.1rem 0.3rem;
    border-radius: 0.2rem;
    font-size: 0.7rem;
    cursor: pointer;
    display: inline-block;
    margin-left: 0.25rem;
}

.long-text-indicator:hover {
    background-color: #138496;
}

.table {
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
    border-collapse: collapse;
}

.table th {
    background-color: var(--table-header-bg);
    border: 1px solid var(--table-border);
    border-bottom: 2px solid var(--table-border);
    padding: 0.25rem;
    font-weight: 600;
    font-size: 0.7rem;
}

.table td {
    border: 1px solid var(--table-border);
    padding: 0.25rem;
    vertical-align: top;
}

.table td, .table th {
    border: 1px solid var(--table-border);
}

/* Ensure table borders are always visible */
.table-bordered {
    border: 1px solid var(--table-border);
}

.table-bordered th,
.table-bordered td {
    border: 1px solid var(--table-border);
}

/* Override Bootstrap defaults to ensure borders show */
.table > :not(caption) > * > * {
    border-bottom-width: 1px;
    border-color: var(--table-border);
}

/* Make sure borders are visible in both themes */
[data-theme="dark"] .table th,
[data-theme="dark"] .table td {
    border-color: #444;
}

.table th,
.table td {
    border-color: #adb5bd !important;
}

/* Force table borders to be visible */
.table-bordered > :not(caption) > * {
    border-width: 1px 0;
}

.table-bordered > :not(caption) > * > * {
    border-width: 0 1px;
}

.table > :not(caption) > * > * {
    border-bottom-width: 1px !important;
    border-color: var(--table-border) !important;
}

/* Specific overrides for word-style tables */
.word-table th,
.word-table td {
    border: 1px solid var(--table-border) !important;
}

.word-table {
    border-collapse: collapse !important;
}

.table-responsive {
    border: 1px solid var(--table-border);
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
}

.field-view-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--modal-bg);
    color: var(--modal-text);
    border: 1px solid var(--table-border);
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    z-index: 1055;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    display: none;
    flex-direction: column;
}

.field-view-header {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--table-border);
    background-color: var(--table-header-bg);
    border-radius: 0.5rem 0.5rem 0 0;
}

.field-view-title {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
}

.field-view-content {
    padding: 1rem;
    overflow-y: auto;
    flex: 1;
    min-height: 200px;
}

.field-view-content pre {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
}

.field-view-footer {
    padding: 0.75rem 1rem;
    border-top: 1px solid var(--table-border);
    display: flex;
    justify-content: flex-end;
}

.btn-close-field {
    background: var(--danger-color);
    color: white;
    border: none;
    padding: 0.25rem 0.75rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.9rem;
}

.btn-close-field:hover {
    background: #c82333;
}

.field-view-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1050;
    display: none;
}

.view-toggle-container {
    display: inline-block;
    margin-left: 1rem;
}

.view-toggle-label {
    font-size: 0.9rem;
    margin-right: 0.5rem;
    font-weight: 500;
}

.view-toggle-select {
    display: inline-block;
    width: auto;
    font-size: 0.85rem;
    padding: 0.25rem 0.5rem;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

#downloadHtmlBtn {
    transition: all 0.2s ease-in-out;
}

#downloadHtmlBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.word-document-style {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--bs-body-color);
}

.word-section {
    margin-bottom: 1.5rem;
    padding: 0.75rem;
    border: 1px solid var(--table-border);
    border-radius: 0.375rem;
    background-color: var(--bs-card-bg);
}

.word-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.25rem;
}

.word-subsection {
    margin-bottom: 1rem;
    padding: 0.5rem;
    border-left: 3px solid var(--info-color);
    background-color: rgba(23, 162, 184, 0.05);
}

.word-subsection-title {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--info-color);
}

.word-data-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
    font-size: 0.85rem;
    border: 1px solid var(--table-border);
}

.word-data-table th {
    background-color: var(--table-header-bg);
    color: var(--bs-body-color);
    font-weight: 600;
    padding: 0.5rem;
    border: 1px solid var(--table-border);
    text-align: left;
}

.word-data-table td {
    padding: 0.4rem;
    border: 1px solid var(--table-border);
    vertical-align: top;
}

.word-data-table th, .word-data-table td {
    border: 1px solid var(--table-border);
}

.word-data-table tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

[data-theme="dark"] .word-data-table tr:nth-child(even) {
    background-color: rgba(255, 255, 255, 0.02);
}

.word-list {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.word-list li {
    margin-bottom: 0.25rem;
    line-height: 1.4;
}

.word-property {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: flex-start;
}

.word-property-name {
    font-weight: 500;
    color: var(--primary-color);
    min-width: 120px;
    margin-right: 0.75rem;
}

.word-property-value {
    flex: 1;
    word-break: break-word;
}

.word-nested-object {
    margin-left: 1rem;
    padding: 0.5rem;
    border-left: 2px solid var(--secondary-color);
    background-color: rgba(108, 117, 125, 0.05);
}

.word-array-container {
    margin: 0.5rem 0;
}

.word-simple-value {
    font-family: 'Courier New', monospace;
    background-color: rgba(0, 123, 255, 0.1);
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.9rem;
}

.word-simple-value.string {
    color: var(--success-color);
}

.word-simple-value.number {
    color: var(--info-color);
}

.word-simple-value.boolean {
    color: var(--warning-color);
}

.word-simple-value.null {
    color: var(--secondary-color);
    font-style: italic;
}`;
    }

    function downloadAsHtml() {
        try {
            // Get the current CSS content from the existing stylesheet
            const cssContent = getCssContent();

            // Get the current rendered JSON output
            const jsonOutputContent = jsonOutput.innerHTML;

            // Get the current theme
            const isDarkMode = document.body.hasAttribute('data-theme');
            const themeAttribute = isDarkMode ? ' data-theme="dark"' : '';

            // Create the self-contained HTML
            const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON Viewer Export</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
${cssContent}
    </style>
</head>
<body${themeAttribute}>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center my-4">JSON Viewer Export</h1>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">JSON Data</h5>
                    </div>
                    <div class="card-body">
                        <div id="jsonOutput">
${jsonOutputContent}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Field View Modal -->
    <div class="field-view-overlay" id="fieldViewOverlay"></div>
    <div class="field-view-modal" id="fieldViewModal">
        <div class="field-view-header">
            <h5 class="field-view-title">Field Content</h5>
        </div>
        <div class="field-view-content" id="fieldViewContent"></div>
        <div class="field-view-footer">
            <button class="btn-close-field" id="closeFieldViewBottom">Close</button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Minimal JavaScript for the exported HTML
        document.addEventListener('DOMContentLoaded', function() {
            // Field view functionality
            function showFieldView(content) {
                const overlay = document.getElementById('fieldViewOverlay');
                const modal = document.getElementById('fieldViewModal');
                const contentDiv = document.getElementById('fieldViewContent');

                contentDiv.innerHTML = '<pre>' + content + '</pre>';
                overlay.style.display = 'block';
                modal.style.display = 'block';
            }

            function hideFieldView() {
                const overlay = document.getElementById('fieldViewOverlay');
                const modal = document.getElementById('fieldViewModal');
                overlay.style.display = 'none';
                modal.style.display = 'none';
            }

            // Add event listeners for long text indicators
            document.querySelectorAll('.long-text-indicator').forEach(indicator => {
                indicator.addEventListener('click', function(e) {
                    e.stopPropagation();
                    showFieldView(this.getAttribute('data-full-text'));
                });
            });

            // Close modal events
            document.getElementById('fieldViewOverlay').addEventListener('click', hideFieldView);
            document.getElementById('closeFieldViewBottom').addEventListener('click', hideFieldView);
        });
    </script>
</body>
</html>`;

            // Create and trigger download
            const blob = new Blob([htmlContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;

            // Generate filename based on original file and settings
            const now = new Date();
            const timestamp = now.toISOString().slice(0, 19).replace(/[:.]/g, '-');
            const viewMode = currentViewMode === 'transpose' ? 'transpose' : 'normal';
            const baseName = currentFileName ? currentFileName.replace(/\.[^/.]+$/, '') : 'json-data';
            a.download = `${baseName}-${viewMode}-${timestamp}.html`;

            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Error generating HTML download:', error);
            alert('Error generating HTML file. Please try again.');
        }
    }

    // --- Initial Setup ---
    clearSearchBtn.style.display = 'none';
    createFieldViewModal();
});
