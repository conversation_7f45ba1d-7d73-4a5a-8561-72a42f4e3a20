<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON Viewer</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --table-header-bg: #e9ecef;
            --table-border: #dee2e6;
            --expand-btn-bg: #f1f1f1;
            --expand-btn-hover: #e2e6ea;
            --nesting-indent: 20px;
        }

        [data-theme="dark"] {
            --bs-body-bg: #121212;
            --bs-body-color: #e0e0e0;
            --bs-card-bg: #1e1e1e;
            --bs-card-border-color: #333;
            --table-header-bg: #2d2d2d;
            --table-border: #444;
            --expand-btn-bg: #333;
            --expand-btn-hover: #444;
            --bs-border-color: #444;
        }

        body {
            background-color: var(--bs-body-bg);
            color: var(--bs-body-color);
            transition: background-color 0.3s, color 0.3s;
            padding: 20px;
        }

        .card {
            background-color: var(--bs-card-bg);
            border-color: var(--bs-card-border-color);
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: box-shadow 0.3s;
        }

        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .card-header {
            background-color: rgba(0, 0, 0, 0.03);
            border-bottom: 1px solid var(--bs-card-border-color);
            font-weight: 500;
        }

        [data-theme="dark"] .card-header {
            background-color: #2a2a2a;
        }

        .table-container {
            overflow-x: auto;
            border-radius: 0.375rem;
            border: 1px solid var(--table-border);
        }

        .table {
            margin-bottom: 0;
            background-color: var(--bs-card-bg);
        }

        .table th {
            background-color: var(--table-header-bg);
            position: sticky;
            top: 0;
            border-bottom: 2px solid var(--table-border);
        }

        .table td, .table th {
            border-color: var(--table-border);
            vertical-align: middle;
        }

        .nested-object {
            display: block;
            margin-top: 0.5rem;
            padding: 0.5rem;
            background-color: rgba(0, 0, 0, 0.02);
            border-radius: 0.25rem;
            border: 1px solid var(--table-border);
        }

        [data-theme="dark"] .nested-object {
            background-color: #2a2a2a;
        }

        .json-key {
            font-weight: 600;
            color: var(--primary-color);
        }

        [data-theme="dark"] .json-key {
            color: #6ea8fe;
        }

        .json-value {
            word-break: break-word;
        }

        .json-value.string {
            color: var(--success-color);
        }

        .json-value.number {
            color: var(--info-color);
        }

        .json-value.boolean {
            color: var(--warning-color);
        }

        .json-value.null {
            color: var(--secondary-color);
            font-style: italic;
        }

        .text-truncate-cell {
            max-width: 200px;
        }

        .badge-sm {
            font-size: 0.7em;
        }

        /* Nested structure styling */
        .nested-structure {
            padding: 1rem;
        }

        .nested-item {
            margin-bottom: 1rem;
        }

        .nested-item .card-header {
            background-color: rgba(0, 0, 0, 0.03);
        }

        [data-theme="dark"] .nested-item .card-header {
            background-color: #2a2a2a;
        }

        .nested-object .row {
            margin-bottom: 0.5rem;
        }

        .nested-object .row:last-child {
            margin-bottom: 0;
        }

        /* Improved nesting with better hierarchy */
        .nested-level-0 { margin-left: 0; }
        .nested-level-1 { margin-left: calc(var(--nesting-indent) * 1); }
        .nested-level-2 { margin-left: calc(var(--nesting-indent) * 2); }
        .nested-level-3 { margin-left: calc(var(--nesting-indent) * 3); }
        .nested-level-4 { margin-left: calc(var(--nesting-indent) * 4); }
        .nested-level-5 { margin-left: calc(var(--nesting-indent) * 5); }

        .nested-object .nested-object {
            border-left: 2px solid var(--table-border);
            padding-left: 1rem;
            margin: 0.5rem 0;
        }

        [data-theme="dark"] .nested-object .nested-object {
            border-left-color: #444;
        }

        /* Long text handling - tooltip only approach */
        .long-text-container {
            cursor: pointer;
            border: 1px dashed transparent;
            padding: 5px;
            border-radius: 4px;
        }

        .long-text-container:hover {
            border-color: var(--primary-color);
            background-color: rgba(0, 123, 255, 0.05);
        }

        /* Tooltip for full text */
        [title] {
            position: relative;
        }

        [title]:hover::after {
            content: attr(title);
            position: absolute;
            bottom: 100%;
            left: 0;
            background-color: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            max-width: 300px;
            z-index: 1000;
            font-size: 0.9em;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .text-truncate-cell {
                max-width: 100px;
            }
            
            .table-sm td, .table-sm th {
                padding: 0.25rem 0.5rem;
            }
            
            .nested-structure {
                padding: 0.5rem;
            }
            
            .nested-level-1 { margin-left: 10px; }
            .nested-level-2 { margin-left: 20px; }
            .nested-level-3 { margin-left: 30px; }
            .nested-level-4 { margin-left: 40px; }
            .nested-level-5 { margin-left: 50px; }
        }

        /* Card styling for nested items */
        .nested-object .card {
            margin-bottom: 0.5rem;
            background-color: rgba(0, 0, 0, 0.02);
        }

        [data-theme="dark"] .nested-object .card {
            background-color: #2a2a2a;
        }

        .nested-object .card:last-child {
            margin-bottom: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 10px;
            background-color: rgba(0, 123, 255, 0.1);
        }
        
        [data-theme="dark"] .header {
            background-color: rgba(100, 150, 255, 0.1);
        }
        
        .content-container {
            max-width: 1200px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div class="content-container">
        <div class="header">
            <h1>JSON Viewer</h1>
            <p class="text-muted">Generated on 2025-08-03 09:36:53</p>
        </div>
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">JSON Data</h5>
            </div>
            <div class="card-body">
                <div id="jsonOutput">
                    <div class="nested-structure"><div class="nested-object nested-level-0"><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">users</strong></div><div class="col-sm-9"><div class="nested-level-1"><div class="mb-2"><div class="card"><div class="card-body p-2"><strong>Item 1</strong><div class="nested-object nested-level-2"><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">id</strong></div><div class="col-sm-9"><span class="json-value number">1</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">name</strong></div><div class="col-sm-9"><span class="json-value string">John Doe</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">email</strong></div><div class="col-sm-9"><span class="json-value string"><EMAIL></span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">age</strong></div><div class="col-sm-9"><span class="json-value number">32</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">active</strong></div><div class="col-sm-9"><span class="json-value boolean">true</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">address</strong></div><div class="col-sm-9"><div class="nested-object nested-level-3"><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">street</strong></div><div class="col-sm-9"><span class="json-value string">123 Main St</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">city</strong></div><div class="col-sm-9"><span class="json-value string">New York</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">zipcode</strong></div><div class="col-sm-9"><span class="json-value string">10001</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">coordinates</strong></div><div class="col-sm-9"><div class="nested-object nested-level-4"><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">lat</strong></div><div class="col-sm-9"><span class="json-value number">40.7128</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">lng</strong></div><div class="col-sm-9"><span class="json-value number">-74.006</span></div></div></div></div></div></div></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">hobbies</strong></div><div class="col-sm-9"><span class="badge bg-info">Array[3]</span> [<span class="json-value string">reading</span>, <span class="json-value string">swimming</span>, <span class="json-value string">coding</span>]</div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">metadata</strong></div><div class="col-sm-9"><div class="nested-object nested-level-3"><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">lastLogin</strong></div><div class="col-sm-9"><span class="json-value string">2025-08-01T10:30:00Z</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">preferences</strong></div><div class="col-sm-9"><div class="nested-object nested-level-4"><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">theme</strong></div><div class="col-sm-9"><span class="json-value string">dark</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">language</strong></div><div class="col-sm-9"><span class="json-value string">en</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">notifications</strong></div><div class="col-sm-9"><span class="json-value boolean">true</span></div></div></div></div></div></div></div></div></div></div></div></div><div class="mb-2"><div class="card"><div class="card-body p-2"><strong>Item 2</strong><div class="nested-object nested-level-2"><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">id</strong></div><div class="col-sm-9"><span class="json-value number">2</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">name</strong></div><div class="col-sm-9"><span class="json-value string">Jane Smith</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">email</strong></div><div class="col-sm-9"><span class="json-value string"><EMAIL></span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">age</strong></div><div class="col-sm-9"><span class="json-value number">28</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">active</strong></div><div class="col-sm-9"><span class="json-value boolean">false</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">address</strong></div><div class="col-sm-9"><div class="nested-object nested-level-3"><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">street</strong></div><div class="col-sm-9"><span class="json-value string">456 Oak Ave</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">city</strong></div><div class="col-sm-9"><span class="json-value string">Los Angeles</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">zipcode</strong></div><div class="col-sm-9"><span class="json-value string">90210</span></div></div></div></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">hobbies</strong></div><div class="col-sm-9"><span class="badge bg-info">Array[2]</span> [<span class="json-value string">painting</span>, <span class="json-value string">hiking</span>]</div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">metadata</strong></div><div class="col-sm-9"><div class="nested-object nested-level-3"><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">lastLogin</strong></div><div class="col-sm-9"><span class="json-value string">2025-07-28T15:45:00Z</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">preferences</strong></div><div class="col-sm-9"><div class="nested-object nested-level-4"><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">theme</strong></div><div class="col-sm-9"><span class="json-value string">light</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">language</strong></div><div class="col-sm-9"><span class="json-value string">es</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">notifications</strong></div><div class="col-sm-9"><span class="json-value boolean">false</span></div></div></div></div></div></div></div></div></div></div></div></div><div class="mb-2"><div class="card"><div class="card-body p-2"><strong>Item 3</strong><div class="nested-object nested-level-2"><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">id</strong></div><div class="col-sm-9"><span class="json-value number">3</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">name</strong></div><div class="col-sm-9"><span class="json-value string">Bob Johnson</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">email</strong></div><div class="col-sm-9"><span class="json-value string"><EMAIL></span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">age</strong></div><div class="col-sm-9"><span class="json-value number">45</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">active</strong></div><div class="col-sm-9"><span class="json-value boolean">true</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">address</strong></div><div class="col-sm-9"><div class="nested-object nested-level-3"><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">street</strong></div><div class="col-sm-9"><span class="json-value string">789 Pine Rd</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">city</strong></div><div class="col-sm-9"><span class="json-value string">Chicago</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">zipcode</strong></div><div class="col-sm-9"><span class="json-value string">60601</span></div></div></div></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">hobbies</strong></div><div class="col-sm-9"><span class="badge bg-info">Array[5]</span> [<span class="json-value string">cooking</span>, <span class="json-value string">gardening</span>, <span class="json-value string">photography</span>, <span class="json-value string">traveling</span>, <span class="json-value string">music</span>]</div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">metadata</strong></div><div class="col-sm-9"><div class="nested-object nested-level-3"><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">lastLogin</strong></div><div class="col-sm-9"><span class="json-value string">2025-08-02T09:15:00Z</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">preferences</strong></div><div class="col-sm-9"><div class="nested-object nested-level-4"><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">theme</strong></div><div class="col-sm-9"><span class="json-value string">auto</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">language</strong></div><div class="col-sm-9"><span class="json-value string">fr</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">notifications</strong></div><div class="col-sm-9"><span class="json-value boolean">true</span></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">settings</strong></div><div class="col-sm-9"><div class="nested-object nested-level-1"><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">features</strong></div><div class="col-sm-9"><div class="nested-object nested-level-2"><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">enableDarkMode</strong></div><div class="col-sm-9"><span class="json-value boolean">true</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">enableNotifications</strong></div><div class="col-sm-9"><span class="json-value boolean">true</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">maxUsers</strong></div><div class="col-sm-9"><span class="json-value number">1000</span></div></div></div></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">timestamps</strong></div><div class="col-sm-9"><div class="nested-object nested-level-2"><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">created</strong></div><div class="col-sm-9"><span class="json-value string">2025-01-15T08:00:00Z</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">updated</strong></div><div class="col-sm-9"><span class="json-value string">2025-08-01T12:00:00Z</span></div></div></div></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">version</strong></div><div class="col-sm-9"><span class="json-value string">1.2.3</span></div></div></div></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">statistics</strong></div><div class="col-sm-9"><div class="nested-object nested-level-1"><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">totalUsers</strong></div><div class="col-sm-9"><span class="json-value number">3</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">activeUsers</strong></div><div class="col-sm-9"><span class="json-value number">2</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">averageAge</strong></div><div class="col-sm-9"><span class="json-value number">35</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">byCity</strong></div><div class="col-sm-9"><div class="nested-object nested-level-2"><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">New York</strong></div><div class="col-sm-9"><span class="json-value number">1</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">Los Angeles</strong></div><div class="col-sm-9"><span class="json-value number">1</span></div></div><div class="row mb-2"><div class="col-sm-3"><strong class="json-key">Chicago</strong></div><div class="col-sm-9"><span class="json-value number">1</span></div></div></div></div></div></div></div></div></div></div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Add dark mode toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Check for dark mode preference in localStorage
            if (localStorage.getItem('darkMode') === 'enabled') {
                document.body.setAttribute('data-theme', 'dark');
            }
            
            // Add dark mode toggle to the page
            const header = document.querySelector('.header');
            const toggleContainer = document.createElement('div');
            toggleContainer.className = 'form-check form-switch';
            toggleContainer.innerHTML = `
                <input class="form-check-input" type="checkbox" id="darkModeToggle">
                <label class="form-check-label" for="darkModeToggle">Dark Mode</label>
            `;
            header.appendChild(toggleContainer);
            
            const darkModeToggle = document.getElementById('darkModeToggle');
            if (localStorage.getItem('darkMode') === 'enabled') {
                darkModeToggle.checked = true;
            }
            
            darkModeToggle.addEventListener('change', function() {
                if (this.checked) {
                    document.body.setAttribute('data-theme', 'dark');
                    localStorage.setItem('darkMode', 'enabled');
                } else {
                    document.body.removeAttribute('data-theme');
                    localStorage.setItem('darkMode', 'disabled');
                }
            });
        });
    </script>
</body>
</html>