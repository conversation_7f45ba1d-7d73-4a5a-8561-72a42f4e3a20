#!/usr/bin/env python3
"""
Test script to verify the transpose functionality is working correctly.
This script will create a test HTML file and open it in the browser.
"""

import json
import os
import webbrowser
from pathlib import Path

def create_test_data():
    """Create comprehensive test data to verify transpose functionality."""
    return {
        "simple_object": {
            "name": "<PERSON>",
            "age": 30,
            "active": True
        },
        "nested_object": {
            "user": {
                "id": 1,
                "profile": {
                    "name": "<PERSON>",
                    "email": "<EMAIL>",
                    "preferences": {
                        "theme": "dark",
                        "notifications": True
                    }
                },
                "address": {
                    "street": "123 Main St",
                    "city": "New York",
                    "coordinates": {
                        "lat": 40.7128,
                        "lng": -74.006
                    }
                }
            }
        },
        "array_of_objects": [
            {
                "id": 1,
                "name": "Product A",
                "price": 29.99,
                "tags": ["electronics", "gadget"]
            },
            {
                "id": 2,
                "name": "Product B",
                "price": 49.99,
                "tags": ["home", "kitchen"]
            }
        ],
        "mixed_array": [
            "string_item",
            42,
            True,
            {"nested": "object"},
            ["nested", "array"]
        ],
        "empty_structures": {
            "empty_object": {},
            "empty_array": [],
            "null_value": None
        }
    }

def save_test_json():
    """Save test data to a JSON file."""
    test_data = create_test_data()
    with open('test_data.json', 'w') as f:
        json.dump(test_data, f, indent=2)
    print("✅ Test data saved to test_data.json")

def run_tests():
    """Run the transpose functionality tests."""
    print("🧪 Testing JSON Viewer Transpose Functionality")
    print("=" * 50)
    
    # Create test data
    save_test_json()
    
    # Check if main files exist
    required_files = ['index.html', 'viewer.js', 'style.css']
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} exists")
        else:
            print(f"❌ {file} missing")
            return False
    
    # Open the test page
    test_url = f"file:///{os.path.abspath('test_transpose.html')}"
    print(f"\n🌐 Opening test page: {test_url}")
    
    print("\n📋 Manual Test Instructions:")
    print("1. Click 'Load Sample JSON' button")
    print("2. Verify Normal View shows nested structure")
    print("3. Switch to 'Transpose View' from dropdown")
    print("4. Verify transpose view shows:")
    print("   - Flattened key paths (e.g., 'users[0].name')")
    print("   - Proper values for each path")
    print("   - Nested objects expanded as key-value pairs")
    print("   - Arrays shown with proper formatting")
    print("5. Test with the actual sample.json file using main index.html")
    
    return True

if __name__ == "__main__":
    success = run_tests()
    if success:
        print("\n✅ Test setup complete! Please verify functionality manually in the browser.")
    else:
        print("\n❌ Test setup failed!")
