<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON Viewer - Transpose Test</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center my-4">JSON Viewer - Transpose Test</h1>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Test Controls</h5>
                    </div>
                    <div class="card-body">
                        <div class="mt-3">
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="darkModeToggle">
                                <label class="form-check-label" for="darkModeToggle">
                                    Dark Mode
                                </label>
                            </div>
                            <div class="view-toggle-container">
                                <label class="view-toggle-label" for="viewToggle">View Mode:</label>
                                <select class="form-select view-toggle-select" id="viewToggle">
                                    <option value="normal">Normal View</option>
                                    <option value="transpose" selected>Word-Style Transpose View</option>
                                </select>
                            </div>
                            <button class="btn btn-primary mt-2" id="loadSampleBtn">Load Sample JSON</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">JSON Data</h5>
                    </div>
                    <div class="card-body">
                        <div id="jsonOutput">
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-file-alt fa-3x mb-3"></i>
                                <p>Click "Load Sample JSON" to test the transpose functionality</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Field View Modal -->
    <div class="field-view-overlay" id="fieldViewOverlay"></div>
    <div class="field-view-modal" id="fieldViewModal">
        <div class="field-view-header">
            <h5 class="field-view-title">Field Content</h5>
        </div>
        <div class="field-view-content" id="fieldViewContent"></div>
        <div class="field-view-footer">
            <button class="btn-close-field" id="closeFieldViewBottom">Close</button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="viewer.js"></script>
    <script>
        // Add test functionality
        document.addEventListener('DOMContentLoaded', function() {
            const loadSampleBtn = document.getElementById('loadSampleBtn');
            
            loadSampleBtn.addEventListener('click', function() {
                // Sample JSON data for testing
                const sampleData = {
                    "users": [
                        {
                            "id": 1,
                            "name": "John Doe",
                            "email": "<EMAIL>",
                            "age": 32,
                            "active": true,
                            "address": {
                                "street": "123 Main St",
                                "city": "New York",
                                "zipcode": "10001",
                                "coordinates": {
                                    "lat": 40.7128,
                                    "lng": -74.006
                                }
                            },
                            "hobbies": ["reading", "swimming", "coding"]
                        },
                        {
                            "id": 2,
                            "name": "Jane Smith",
                            "email": "<EMAIL>",
                            "age": 28,
                            "active": false,
                            "address": {
                                "street": "456 Oak Ave",
                                "city": "Los Angeles",
                                "zipcode": "90210"
                            },
                            "hobbies": ["painting", "hiking"]
                        }
                    ],
                    "settings": {
                        "features": {
                            "enableDarkMode": true,
                            "enableNotifications": true,
                            "maxUsers": 1000
                        },
                        "version": "1.2.3"
                    }
                };
                
                // Use the renderJSON function from viewer.js
                if (typeof renderJSON === 'function') {
                    renderJSON(sampleData);
                } else {
                    // Fallback: trigger the render through the global scope
                    window.currentJsonData = sampleData;
                    if (window.renderJSON) {
                        window.renderJSON(sampleData);
                    }
                }
            });
        });
    </script>
</body>
</html>
