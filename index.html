<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON Viewer</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center my-4">JSON Viewer</h1>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Load JSON File</h5>
                    </div>
                    <div class="card-body">
                        <div class="input-group">
                            <input type="file" class="form-control" id="jsonFile" accept=".json">
                            <button class="btn btn-primary" type="button" id="loadFileBtn">
                                <i class="fas fa-file-import"></i> Load JSON
                            </button>
                        </div>
                        <div class="mt-3">
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="darkModeToggle">
                                <label class="form-check-label" for="darkModeToggle">
                                    Dark Mode
                                </label>
                            </div>
                            <div class="view-toggle-container">
                                <label class="view-toggle-label" for="viewToggle">View Mode:</label>
                                <select class="form-select view-toggle-select" id="viewToggle">
                                    <option value="normal">Normal View</option>
                                    <option value="transpose">Word-Style Transpose View</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Filter Data</h5>
                    </div>
                    <div class="card-body">
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchInput" placeholder="Search in JSON data...">
                            <button class="btn btn-outline-secondary" type="button" id="clearSearchBtn">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">JSON Data</h5>
                        <button class="btn btn-success btn-sm" id="downloadHtmlBtn" style="display: none;">
                            <i class="fas fa-download"></i> Download HTML
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="jsonOutput">
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-file-alt fa-3x mb-3"></i>
                                <p>Please load a JSON file to view its contents</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Field View Modal -->
    <div class="field-view-overlay" id="fieldViewOverlay"></div>
    <div class="field-view-modal" id="fieldViewModal">
        <div class="field-view-header">
            <h5 class="field-view-title">Field Content</h5>
        </div>
        <div class="field-view-content" id="fieldViewContent"></div>
        <div class="field-view-footer">
            <button class="btn-close-field" id="closeFieldViewBottom">Close</button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="viewer.js"></script>
</body>
</html>
