/* style.css - Updated with global view toggle */

:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --table-header-bg: #e9ecef;
    --table-border: #adb5bd;
    --nesting-indent: 12px; /* Very compact indentation */
    --modal-bg: #ffffff;
    --modal-text: #212529;
}

[data-theme="dark"] {
    --bs-body-bg: #121212;
    --bs-body-color: #e0e0e0;
    --bs-card-bg: #1e1e1e;
    --bs-card-border-color: #333;
    --table-header-bg: #2d2d2d;
    --table-border: #444;
    --bs-border-color: #444;
    --modal-bg: #1e1e1e;
    --modal-text: #e0e0e0;
}

body {
    background-color: var(--bs-body-bg);
    color: var(--bs-body-color);
    transition: background-color 0.3s, color 0.3s;
    padding: 5px;
    font-size: 0.85rem;
}

.card {
    background-color: var(--bs-card-bg);
    border-color: var(--bs-card-border-color);
    box-shadow: 0 0.05rem 0.1rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.2s;
    margin-bottom: 5px;
}

.card:hover {
    box-shadow: 0 0.2rem 0.4rem rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid var(--bs-card-border-color);
    font-weight: 500;
    padding: 0.3rem 0.6rem;
    font-size: 0.9rem;
}

[data-theme="dark"] .card-header {
    background-color: #2a2a2a;
}

.table-container {
    overflow-x: auto;
    border-radius: 0.25rem;
    border: 1px solid var(--table-border);
    margin-bottom: 0.5rem;
}

.table {
    margin-bottom: 0;
    background-color: var(--bs-card-bg);
    font-size: 0.8rem;
    border-collapse: collapse;
}

.table th {
    background-color: var(--table-header-bg);
    position: sticky;
    top: 0;
    border: 1px solid var(--table-border);
    border-bottom: 2px solid var(--table-border);
}

.table td, .table th {
    border: 1px solid var(--table-border);
    vertical-align: top;
    padding: 0.25rem 0.3rem;
}

.table td {
    border-top: 1px solid var(--table-border);
    border-bottom: 1px solid var(--table-border);
    border-left: 1px solid var(--table-border);
    border-right: 1px solid var(--table-border);
}

/* Ensure table borders are always visible */
.table-bordered {
    border: 1px solid var(--table-border);
}

.table-bordered th,
.table-bordered td {
    border: 1px solid var(--table-border);
}

/* Override Bootstrap defaults to ensure borders show */
.table > :not(caption) > * > * {
    border-bottom-width: 1px;
    border-color: var(--table-border);
}

/* Make sure borders are visible in both themes */
[data-theme="dark"] .table th,
[data-theme="dark"] .table td {
    border-color: #444;
}

.table th,
.table td {
    border-color: #adb5bd !important;
}

/* Force table borders to be visible */
.table-bordered > :not(caption) > * {
    border-width: 1px 0;
}

.table-bordered > :not(caption) > * > * {
    border-width: 0 1px;
}

.table > :not(caption) > * > * {
    border-bottom-width: 1px !important;
    border-color: var(--table-border) !important;
}

/* Specific overrides for word-style tables */
.word-table th,
.word-table td {
    border: 1px solid var(--table-border) !important;
}

.word-table {
    border-collapse: collapse !important;
    border: 1px solid var(--table-border) !important;
}

/* FORCE ALL TABLES TO HAVE BORDERS - Override Bootstrap completely */
.table-bordered,
.table-bordered > :not(caption) > * > * {
    border-width: 1px !important;
    border-style: solid !important;
    border-color: var(--table-border) !important;
}

/* Ensure Bootstrap table-bordered class works */
.table-bordered > :not(caption) > * {
    border-width: 1px 0 !important;
}

.table-bordered > :not(caption) > * > * {
    border-width: 0 1px !important;
}

/* Force visible borders on ALL table elements */
table.table {
    border: 1px solid var(--table-border) !important;
    border-collapse: collapse !important;
}

table.table th,
table.table td {
    border: 1px solid var(--table-border) !important;
    border-collapse: collapse !important;
}

/* Additional fallback for any table */
table {
    border-collapse: collapse !important;
}

table th,
table td {
    border: 1px solid #adb5bd !important;
}

.nested-object {
    display: block;
    margin-top: 0.2rem;
    padding: 0.2rem;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 0.2rem;
    border: 1px solid var(--table-border);
}

[data-theme="dark"] .nested-object {
    background-color: #2a2a2a;
}

.json-key {
    font-weight: 600;
    color: var(--primary-color);
    word-break: break-word;
    font-size: 0.85rem;
}

[data-theme="dark"] .json-key {
    color: #6ea8fe;
}

.json-value {
    word-break: break-word;
    font-size: 0.85rem;
}

.json-value.string {
    color: var(--success-color);
}

.json-value.number {
    color: var(--info-color);
}

.json-value.boolean {
    color: var(--warning-color);
}

.json-value.null {
    color: var(--secondary-color);
    font-style: italic;
}

.text-truncate-cell {
    max-width: 120px;
}

.badge-sm {
    font-size: 0.6em;
}

/* REVERSE indentation - items move LEFT instead of RIGHT */
/* Level 0 is at the RIGHT edge, each level moves LEFT */
.nested-structure {
    padding: 0.3rem;
}

.nested-item {
    margin-bottom: 0.3rem;
}

.nested-item .card-header {
    background-color: rgba(0, 0, 0, 0.03);
    padding: 0.3rem 0.5rem;
}

[data-theme="dark"] .nested-item .card-header {
    background-color: #2a2a2a;
}

.nested-object .row {
    margin-bottom: 0.2rem;
    display: flex;
    align-items: flex-start;
}

.nested-object .row:last-child {
    margin-bottom: 0;
}

/* REVERSE indentation - items move LEFT instead of RIGHT */
.nested-level-0 { 
    margin-right: 0; 
    margin-left: auto; /* Push to right */
    max-width: 95%;
}
.nested-level-1 { 
    margin-right: var(--nesting-indent); 
    margin-left: auto;
    max-width: 90%;
}
.nested-level-2 { 
    margin-right: calc(var(--nesting-indent) * 2); 
    margin-left: auto;
    max-width: 85%;
}
.nested-level-3 { 
    margin-right: calc(var(--nesting-indent) * 3); 
    margin-left: auto;
    max-width: 80%;
}

/* Beyond level 3, we move further LEFT with compact spacing */
.nested-level-4 { 
    margin-right: calc(var(--nesting-indent) * 4); 
    margin-left: auto;
    max-width: 75%;
}
.nested-level-5 { 
    margin-right: calc(var(--nesting-indent) * 5); 
    margin-left: auto;
    max-width: 70%;
}

.nested-level-6, .nested-level-7, .nested-level-8, .nested-level-9, .nested-level-10 {
    margin-right: calc(var(--nesting-indent) * 5); /* Cap at level 5 spacing */
    margin-left: auto;
    max-width: 65%;
    border-right: 1px dashed var(--table-border);
    padding-right: 4px;
    margin-top: 1px;
    margin-bottom: 1px;
}

.nested-object .nested-object {
    border-right: 1px solid var(--table-border);
    padding-right: 0.3rem;
    margin: 0.1rem 0;
}

[data-theme="dark"] .nested-object .nested-object {
    border-right-color: #444;
}

/* View toggle dropdown */
.view-toggle-container {
    display: inline-block;
    margin-left: 10px;
}

.view-toggle-label {
    margin-right: 5px;
    font-size: 0.8rem;
    vertical-align: middle;
}

.view-toggle-select {
    font-size: 0.8rem;
    padding: 0.1rem;
    vertical-align: middle;
}

/* Field view modal for long text - FIXED TRANSPARENT BACKGROUND */
.field-view-modal {
    position: fixed;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    background-color: var(--modal-bg); /* OPAQUE BACKGROUND */
    color: var(--modal-text);
    border: 1px solid var(--table-border);
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
    z-index: 1050;
    display: none;
    flex-direction: column;
}

.field-view-header {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--table-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.field-view-title {
    margin: 0;
    font-size: 1.1rem;
}

.field-view-content {
    flex: 1;
    padding: 1rem;
    overflow: auto;
    white-space: pre-wrap;
    word-break: break-word;
    font-family: monospace;
    font-size: 0.9rem;
}

.field-view-footer {
    padding: 0.5rem 1rem;
    border-top: 1px solid var(--table-border);
    display: flex;
    justify-content: flex-end;
}

.btn-close-field {
    background: var(--danger-color);
    color: white;
    border: none;
    padding: 0.25rem 0.75rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.9rem;
}

.btn-close-field:hover {
    background: #c82333;
}

/* Overlay to dim background */
.field-view-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    display: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .text-truncate-cell {
        max-width: 60px;
    }
    
    .table td, .table th {
        padding: 0.15rem 0.2rem;
        font-size: 0.7rem;
    }
    
    .nested-structure {
        padding: 0.15rem;
    }
    
    .nested-item .card-header {
        padding: 0.2rem 0.4rem;
    }
    
    .nested-object .row {
        margin-bottom: 0.1rem;
    }
    
    /* Even more compact on mobile */
    :root {
        --nesting-indent: 8px;
    }
    
    .nested-level-1 { margin-right: 8px; margin-left: auto; }
    .nested-level-2 { margin-right: 16px; margin-left: auto; }
    .nested-level-3 { margin-right: 24px; margin-left: auto; }
    .nested-level-4 { margin-right: 32px; margin-left: auto; }
    .nested-level-5 { margin-right: 40px; margin-left: auto; }
    
    .json-key, .json-value {
        font-size: 0.75rem;
    }
}

/* Card styling for nested items */
.nested-object .card {
    margin-bottom: 0.2rem;
    background-color: rgba(0, 0, 0, 0.02);
    font-size: 0.8rem;
}

[data-theme="dark"] .nested-object .card {
    background-color: #2a2a2a;
}

.nested-object .card:last-child {
    margin-bottom: 0;
}

/* Header styling */
.header {
    text-align: center;
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 5px;
    background-color: rgba(0, 123, 255, 0.1);
}

[data-theme="dark"] .header {
    background-color: rgba(100, 150, 255, 0.1);
}

/* Long text indicators */
.long-text-indicator {
    cursor: pointer;
    color: var(--primary-color);
    text-decoration: underline;
    font-size: 0.75rem;
    display: inline-block;
    margin-left: 5px;
}

.long-text-indicator:hover {
    color: #0056b3;
}

/* Form elements */
.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

#downloadHtmlBtn {
    transition: all 0.2s ease-in-out;
}

#downloadHtmlBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Enhanced Transpose View Styles */
.array-content {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    background-color: rgba(23, 162, 184, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    border-left: 2px solid var(--info-color);
}

.object-content {
    font-size: 0.8rem;
    background-color: rgba(40, 167, 69, 0.1);
    padding: 4px 6px;
    border-radius: 3px;
    border-left: 2px solid var(--success-color);
}

.object-pair {
    margin: 1px 0;
    line-height: 1.2;
}

.object-pair strong {
    color: var(--primary-color);
    font-weight: 600;
}

.json-value.array {
    color: var(--info-color);
    font-weight: 500;
}

.json-value.object {
    color: var(--success-color);
    font-weight: 500;
}

/* Dark theme adjustments for transpose view */
[data-theme="dark"] .array-content {
    background-color: rgba(23, 162, 184, 0.2);
}

[data-theme="dark"] .object-content {
    background-color: rgba(40, 167, 69, 0.2);
}

[data-theme="dark"] .object-pair strong {
    color: #4dabf7;
}

/* Word Document Style Transpose View */
.word-document-style {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--bs-body-color);
    background-color: var(--bs-body-bg);
    padding: 20px;
    max-width: 100%;
}

.word-section {
    margin-bottom: 25px;
    padding: 15px;
    border-left: 3px solid var(--primary-color);
    background-color: rgba(0, 123, 255, 0.02);
    border-radius: 5px;
}

.word-section.level-0 {
    border-left-color: var(--primary-color);
    background-color: rgba(0, 123, 255, 0.03);
}

.word-section.level-1 {
    border-left-color: var(--success-color);
    background-color: rgba(40, 167, 69, 0.02);
    margin-left: 20px;
}

.word-section.level-2 {
    border-left-color: var(--info-color);
    background-color: rgba(23, 162, 184, 0.02);
    margin-left: 40px;
}

.word-section.level-3 {
    border-left-color: var(--warning-color);
    background-color: rgba(255, 193, 7, 0.02);
    margin-left: 60px;
}

.word-heading {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 15px;
    margin-top: 0;
    border-bottom: 2px solid var(--table-border);
    padding-bottom: 8px;
}

.word-table-container {
    margin: 15px 0;
    overflow-x: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.word-table {
    margin-bottom: 0;
    font-size: 0.9rem;
    background-color: var(--bs-body-bg);
}

.word-table-header {
    background-color: var(--table-header-bg);
    color: var(--bs-body-color);
    font-weight: 600;
    text-transform: capitalize;
    border-bottom: 2px solid var(--table-border);
    padding: 12px 8px;
    white-space: nowrap;
}

.word-table-cell {
    padding: 10px 8px;
    vertical-align: top;
    border-bottom: 1px solid var(--table-border);
    max-width: 200px;
    word-wrap: break-word;
}

.word-property {
    margin: 8px 0;
    padding: 5px 0;
    border-bottom: 1px dotted var(--table-border);
}

.word-property-key {
    font-weight: 600;
    color: var(--primary-color);
    min-width: 120px;
    display: inline-block;
}

.word-property-value {
    color: var(--bs-body-color);
}

.word-list {
    margin: 10px 0;
    padding-left: 25px;
}

.word-list-item {
    margin: 5px 0;
    line-height: 1.4;
}

.word-object-inline {
    font-size: 0.85rem;
}

.word-key-value {
    display: inline-block;
    margin-right: 10px;
    padding: 2px 6px;
    background-color: rgba(0, 123, 255, 0.1);
    border-radius: 3px;
}

.word-content {
    margin: 10px 0;
}

/* Dark theme adjustments for Word style */
[data-theme="dark"] .word-section {
    background-color: rgba(0, 123, 255, 0.05);
}

[data-theme="dark"] .word-section.level-1 {
    background-color: rgba(40, 167, 69, 0.05);
}

[data-theme="dark"] .word-section.level-2 {
    background-color: rgba(23, 162, 184, 0.05);
}

[data-theme="dark"] .word-section.level-3 {
    background-color: rgba(255, 193, 7, 0.05);
}

[data-theme="dark"] .word-key-value {
    background-color: rgba(77, 171, 247, 0.2);
}
