{"simple_object": {"name": "<PERSON>", "age": 30, "active": true}, "nested_object": {"user": {"id": 1, "profile": {"name": "<PERSON>", "email": "<EMAIL>", "preferences": {"theme": "dark", "notifications": true}}, "address": {"street": "123 Main St", "city": "New York", "coordinates": {"lat": 40.7128, "lng": -74.006}}}}, "array_of_objects": [{"id": 1, "name": "Product A", "price": 29.99, "tags": ["electronics", "gadget"]}, {"id": 2, "name": "Product B", "price": 49.99, "tags": ["home", "kitchen"]}], "mixed_array": ["string_item", 42, true, {"nested": "object"}, ["nested", "array"]], "empty_structures": {"empty_object": {}, "empty_array": [], "null_value": null}}