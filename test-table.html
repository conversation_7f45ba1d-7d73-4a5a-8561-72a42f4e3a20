<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Table Border Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <style>
        /* Force red borders for testing */
        table {
            border: 5px solid red !important;
        }
        table th,
        table td {
            border: 2px solid red !important;
            background-color: rgba(255, 0, 0, 0.1) !important;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center my-4">Table Border Test</h1>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Test Tables</h5>
                    </div>
                    <div class="card-body">
                        <h6>Bootstrap Table with table-bordered class:</h6>
                        <table class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Age</th>
                                    <th>City</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>John Doe</td>
                                    <td>32</td>
                                    <td>New York</td>
                                </tr>
                                <tr>
                                    <td>Jane Smith</td>
                                    <td>28</td>
                                    <td>Los Angeles</td>
                                </tr>
                            </tbody>
                        </table>

                        <h6>Word-style Table:</h6>
                        <table class="table table-striped table-bordered word-table">
                            <thead>
                                <tr>
                                    <th class="word-table-header">ID</th>
                                    <th class="word-table-header">Name</th>
                                    <th class="word-table-header">Email</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="word-table-cell">1</td>
                                    <td class="word-table-cell">John Doe</td>
                                    <td class="word-table-cell"><EMAIL></td>
                                </tr>
                                <tr>
                                    <td class="word-table-cell">2</td>
                                    <td class="word-table-cell">Jane Smith</td>
                                    <td class="word-table-cell"><EMAIL></td>
                                </tr>
                            </tbody>
                        </table>

                        <h6>Plain HTML Table:</h6>
                        <table border="1" style="border-collapse: collapse;">
                            <tr>
                                <th style="border: 1px solid black; padding: 8px;">Header 1</th>
                                <th style="border: 1px solid black; padding: 8px;">Header 2</th>
                            </tr>
                            <tr>
                                <td style="border: 1px solid black; padding: 8px;">Data 1</td>
                                <td style="border: 1px solid black; padding: 8px;">Data 2</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
