# Word-Style Transpose View Documentation

## Overview

The Word-Style Transpose View transforms JSON data into a structured document format that mimics how a human would organize the same information in a Microsoft Word document. This creates an intuitive, readable presentation with proper tables, sections, and hierarchical organization.

## Key Features

### 1. **Arrays of Objects → Data Tables**
- **Input**: JSON arrays containing objects (like `users`, `products`, etc.)
- **Output**: Professional tables with:
  - Column headers derived from object keys
  - Each array item becomes a table row
  - Dynamic column generation (handles objects with different properties)
  - Empty columns shown for missing properties

**Example:**
```json
"users": [
  {"id": 1, "name": "<PERSON>", "age": 30},
  {"id": 2, "name": "<PERSON>", "email": "<EMAIL>"}
]
```

**Becomes:**
```
Users
┌────┬──────┬─────┬───────────────────┐
│ ID │ Name │ Age │ Email             │
├────┼──────┼─────┼───────────────────┤
│ 1  │ John │ 30  │ —                 │
│ 2  │ Jane │ —   │ <EMAIL>  │
└────┴──────┴─────┴───────────────────┘
```

### 2. **Nested Objects → Hierarchical Sections**
- **Input**: Nested JSON objects
- **Output**: Word-style sections with:
  - Main headings for top-level objects
  - Subheadings for nested objects
  - Proper indentation and visual hierarchy
  - Section borders and styling

**Example:**
```json
"settings": {
  "features": {
    "darkMode": true,
    "notifications": false
  },
  "version": "1.2.3"
}
```

**Becomes:**
```
Settings
├─ Features
│  ├─ darkMode: true
│  └─ notifications: false
└─ version: 1.2.3
```

### 3. **Intelligent Nested Object Handling**
The system automatically decides whether to:
- **Flatten small nested objects** into table columns (e.g., `address.street`, `address.city`)
- **Create subsections** for complex nested objects

**Decision Criteria:**
- Objects with ≤3 properties and ≤5 total complexity → Flatten
- Larger/more complex objects → Create subsections

### 4. **Simple Arrays → Bulleted Lists**
- **Input**: Arrays of primitive values
- **Output**: Clean bulleted lists

**Example:**
```json
"hobbies": ["reading", "swimming", "coding"]
```

**Becomes:**
```
Hobbies
• reading
• swimming  
• coding
```

## Implementation Details

### Core Functions

1. **`renderWordStyleContent(data, sectionTitle, level)`**
   - Main orchestrator function
   - Determines data type and routes to appropriate renderer
   - Manages hierarchical levels and section titles

2. **`renderArrayAsWordTable(arrayData, sectionTitle, level)`**
   - Converts arrays of objects into HTML tables
   - Analyzes all objects to create comprehensive column set
   - Handles nested object flattening intelligently

3. **`analyzeArrayForColumns(arrayData)`**
   - Examines array structure to determine optimal columns
   - Decides whether to flatten nested objects or keep them complex
   - Generates column metadata for table creation

4. **`renderObjectAsWordSections(obj, sectionTitle, level)`**
   - Creates hierarchical sections for nested objects
   - Manages proper heading levels (H2-H6)
   - Handles mixed content (objects, arrays, primitives)

### Styling Features

- **Professional Typography**: Segoe UI font family for Word-like appearance
- **Hierarchical Colors**: Different border colors for each nesting level
- **Responsive Tables**: Horizontal scrolling for wide tables
- **Dark Theme Support**: All styles adapt to dark/light mode
- **Visual Hierarchy**: Clear section boundaries and indentation

## Usage Instructions

1. **Load JSON Data**: Use the file picker or load sample data
2. **Select View Mode**: Choose "Word-Style Transpose View" from dropdown
3. **Navigate Content**: Scroll through the organized document structure

## Benefits Over Traditional JSON Views

### Traditional JSON View Problems:
- Deep nesting is hard to follow
- Arrays of objects are difficult to compare
- No clear visual hierarchy
- Poor readability for data analysis

### Word-Style Transpose Solutions:
- **Tabular Data**: Easy comparison of array items
- **Clear Hierarchy**: Document-style organization
- **Professional Appearance**: Familiar Word document layout
- **Data Analysis Friendly**: Tables make patterns obvious

## Technical Specifications

### File Structure:
- **viewer.js**: Core transpose logic (lines 95-398)
- **style.css**: Word document styling (lines 469-610)
- **index.html**: Updated dropdown options

### Browser Compatibility:
- Modern browsers with ES6+ support
- Responsive design for mobile/tablet
- Print-friendly styling

### Performance:
- Efficient column analysis algorithm
- Depth-limited recursion prevents infinite loops
- Optimized for large datasets

## Examples

### Complex Nested Structure:
```json
{
  "company": {
    "employees": [
      {
        "id": 1,
        "name": "John Doe",
        "department": "Engineering",
        "address": {
          "street": "123 Main St",
          "city": "New York"
        }
      }
    ],
    "settings": {
      "workHours": "9-5",
      "remote": true
    }
  }
}
```

**Renders as:**
```
Company
├─ Employees
│  ┌────┬──────────┬──────────────┬──────────────┬──────────┐
│  │ ID │ Name     │ Department   │ Address St   │ Address  │
│  ├────┼──────────┼──────────────┼──────────────┼──────────┤
│  │ 1  │ John Doe │ Engineering  │ 123 Main St  │ New York │
│  └────┴──────────┴──────────────┴──────────────┴──────────┘
│
└─ Settings
   ├─ workHours: 9-5
   └─ remote: true
```

This creates an intuitive, professional document that's easy to read and analyze!
